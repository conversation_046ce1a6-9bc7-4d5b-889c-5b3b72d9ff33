{"pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5", "version": "1.2024072200.0"}}, {"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire.git", "state": {"revision": "513364f870f6bfc468f9d2ff0a95caccc10044c5", "version": "5.10.2"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "appsflyerframework-static", "kind": "remoteSourceControl", "location": "https://github.com/AppsFlyerSDK/AppsFlyerFramework-Static", "state": {"revision": "d65bb9fb9f9bd532cffe0061097a55d0ee26473e", "version": "6.17.2"}}, {"identity": "aws-sdk-ios-spm", "kind": "remoteSourceControl", "location": "https://github.com/aws-amplify/aws-sdk-ios-spm", "state": {"revision": "f2e9fbed08dd14a962f8d62d7e302d73b9ecb429", "version": "2.41.0"}}, {"identity": "codablekeychain", "kind": "remoteSourceControl", "location": "https://github.com/ejensen/CodableKeychain.git", "state": {"revision": "a819d7157e2b079db757f3ce50a13dee3dcbe5b8", "version": "1.5.0"}}, {"identity": "crthen", "kind": "remoteSourceControl", "location": "*********************:computerrock/ios/crthen.git", "state": {"revision": "ee1888a2114529d3838a7ab56417e6efa0871f43", "version": "7.0.2-resc"}}, {"identity": "demo-app-ios", "kind": "remoteSourceControl", "location": "https://github.com/1plusX/demo-app-ios.git", "state": {"revision": "17159ee41a3ffe7ce4de05f9032899c71db2f67d", "version": "1.7.2"}}, {"identity": "devicekit", "kind": "remoteSourceControl", "location": "https://github.com/devicekit/DeviceKit.git", "state": {"revision": "513b9d7e7a1bd46504a1009bbab943b75ce2f195", "version": "5.6.0"}}, {"identity": "disk", "kind": "remoteSourceControl", "location": "https://github.com/saoudrizwan/Disk.git", "state": {"revision": "b0cb4fdf23e51849cc2460bdc6de795c3bcca99d", "version": "0.6.4"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk.git", "state": {"revision": "4e62da1e5e6baf61674d3f5ae23d6d60c19f9c4a", "version": "12.0.0"}}, {"identity": "flanimatedimage", "kind": "remoteSourceControl", "location": "https://github.com/Flipboard/FLAnimatedImage.git", "state": {"revision": "d4f07b6f164d53c1212c3e54d6460738b1981e9f", "version": "1.0.17"}}, {"identity": "google-ads-on-device-conversion-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/googleads/google-ads-on-device-conversion-ios-sdk", "state": {"revision": "428d8bb138e00f9a3f4f61cc6cd8863607524f65", "version": "2.1.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "63c18311aac00032f15f5ce431c9e83ada96c386", "version": "12.0.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "60da361632d0de02786f709bdc0c4df340f7613e", "version": "8.1.0"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71", "version": "1.69.0"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "c756a29784521063b6a1202907e2cc47f41b667c", "version": "4.5.0"}}, {"identity": "icarousel", "kind": "remoteSourceControl", "location": "https://github.com/kunalverma25/iCarousel.git", "state": {"revision": "d9c9c8dd75efc4f77667e3c22c42812f5e72eb25", "version": "1.8.4"}}, {"identity": "immutable", "kind": "remoteSourceControl", "location": "https://github.com/devxoul/Immutable.git", "state": {"revision": "3701f738a11b8f65b6f740ffd0978d200b466c4a", "version": "0.6.0"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe", "version": "101.0.0"}}, {"identity": "ios-library", "kind": "remoteSourceControl", "location": "https://github.com/urbanairship/ios-library", "state": {"revision": "2f2b78351e1ac8d2c74028c8b98490e1c353c690", "version": "19.8.1"}}, {"identity": "keychain-swift", "kind": "remoteSourceControl", "location": "https://github.com/evgenyneu/keychain-swift.git", "state": {"revision": "5e1b02b6a9dac2a759a1d5dbc175c86bd192a608", "version": "24.0.0"}}, {"identity": "kingfisher", "kind": "remoteSourceControl", "location": "https://github.com/onevcat/Kingfisher.git", "state": {"revision": "2015fda791daa72c8058619545a593bf8c1dd59f", "version": "8.5.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "logger", "kind": "remoteSourceControl", "location": "https://github.com/shibapm/Logger", "state": {"revision": "53c3ecca5abe8cf46697e33901ee774236d94cce", "version": "0.2.3"}}, {"identity": "moya", "kind": "remoteSourceControl", "location": "https://github.com/Moya/Moya.git", "state": {"revision": "d27767c3624cc64a45bb371b267b89c92d70c670", "version": "14.0.1"}}, {"identity": "moyasugar", "kind": "remoteSourceControl", "location": "https://github.com/devxoul/MoyaSugar.git", "state": {"revision": "079f25af9798977885058895085051379755bc7c", "version": "1.3.3"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "nimble", "kind": "remoteSourceControl", "location": "https://github.com/Quick/Nimble.git", "state": {"revision": "7a46a5fc86cb917f69e3daf79fcb045283d8f008", "version": "8.1.2"}}, {"identity": "ohhttpstubs", "kind": "remoteSourceControl", "location": "https://github.com/AliSoftware/OHHTTPStubs.git", "state": {"revision": "12f19662426d0434d6c330c6974d53e2eb10ecd9", "version": "9.1.0"}}, {"identity": "packageconfig", "kind": "remoteSourceControl", "location": "https://github.com/shibapm/PackageConfig.git", "state": {"revision": "58523193c26fb821ed1720dcd8a21009055c7cdb", "version": "1.1.3"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "quick", "kind": "remoteSourceControl", "location": "https://github.com/Quick/Quick.git", "state": {"revision": "09b3becb37cb2163919a3842a4c5fa6ec7130792", "version": "2.2.1"}}, {"identity": "reachability.swift", "kind": "remoteSourceControl", "location": "https://github.com/ashleymills/Reachability.swift", "state": {"revision": "21d1dc412cfecbe6e34f1f4c4eb88d3f912654a6", "version": "5.2.4"}}, {"identity": "reactiveswift", "kind": "remoteSourceControl", "location": "https://github.com/Moya/ReactiveSwift.git", "state": {"revision": "f195d82bb30e412e70446e2b4a77e1b514099e88", "version": "6.1.0"}}, {"identity": "rocket", "kind": "remoteSourceControl", "location": "https://github.com/shibapm/Rocket", "state": {"revision": "32af356776108ee7289884874a0eb63ceacb9e5d", "version": "1.3.0"}}, {"identity": "rxswift", "kind": "remoteSourceControl", "location": "https://github.com/ReactiveX/RxSwift.git", "state": {"revision": "cec68169a048a079f461ba203fe85636548d7a89", "version": "5.1.3"}}, {"identity": "stevia", "kind": "remoteSourceControl", "location": "https://github.com/freshOS/Stevia", "state": {"revision": "a925d9b0087536c4ece110109c7815c988516135", "version": "6.2.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "d72aed98f8253ec1aa9ea1141e28150f408cf17f", "version": "1.29.0"}}, {"identity": "swiftshell", "kind": "remoteSourceControl", "location": "https://github.com/kareman/SwiftShell", "state": {"revision": "99680b2efc7c7dbcace1da0b3979d266f02e213c", "version": "5.1.0"}}, {"identity": "then", "kind": "remoteSourceControl", "location": "https://github.com/devxoul/Then.git", "state": {"revision": "e421a7b3440a271834337694e6050133a3958bc7", "version": "2.7.0"}}, {"identity": "usercentrics-spm-sdk", "kind": "remoteSourceControl", "location": "https://bitbucket.org/usercentricscode/usercentrics-spm-sdk", "state": {"revision": "990b2d28fa164bdee97cbd23163c2841f565cd2d", "version": "2.21.1"}}, {"identity": "usercentrics-spm-ui", "kind": "remoteSourceControl", "location": "https://bitbucket.org/usercentricscode/usercentrics-spm-ui", "state": {"revision": "005cc43a9a88c5ba7a07f5628ea98b4fe86765ff", "version": "2.21.1"}}, {"identity": "yams", "kind": "remoteSourceControl", "location": "https://github.com/jpsim/Yams", "state": {"revision": "3d6871d5b4a5cd519adf233fbb576e0a2af71c17", "version": "5.4.0"}}], "version": 2}