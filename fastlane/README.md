fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## iOS

### ios setup

```sh
[bundle exec] fastlane ios setup
```

Generate tokens and prepare project

#### Example:

```
bundle exec fastlane setup --env computerBob
```

### ios setup_all

```sh
[bundle exec] fastlane ios setup_all
```

Setup all environments

### ios preprod

```sh
[bundle exec] fastlane ios preprod
```

Build for pre-production testing and distribute via Firebase

Requires environment to be set (e.g., --env computerBob)

### ios prod

```sh
[bundle exec] fastlane ios prod
```

Build for production and upload to App Store

Requires environment to be set (e.g., --env computerBob)

### ios build_all

```sh
[bundle exec] fastlane ios build_all
```

Build all environments

#### Example:

```
fastlane build_all type:preprod
```

#### Options:

 * **type**: Either `preprod` or `prod`, determines which lane to run

### ios bump_version

```sh
[bundle exec] fastlane ios bump_version
```

Bump marketing version (minor or patch) for all targets

#### Example:

```
fastlane bump_version type:patch
```

#### Options:

 * **type**: Either `minor` or `patch`. Major version is preserved

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
