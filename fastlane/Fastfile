# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

require 'dotenv'
require 'xcodeproj'

default_platform(:ios)

platform :ios do
  before_all do |lane, params|
    ENV['BUILD_OUT_DIR'] = "./Build/Out/#{ENV['SCHEME']}"
    ENV['GOOGLE_CREDENTIALS'] = "./fastlane/Credentials/#{ENV['SCHEME']}/firebase-service.json"
  end

  desc "Generate tokens and prepare project"
  desc "#### Example:"
  desc "```\nbundle exec fastlane setup --env computerBob\n```"
  lane :setup do
    project_dir = File.dirname(File.expand_path('.'))
    sh("#{project_dir}/Script/wls #{ENV['SCHEME']} -p ios -x #{project_dir}/RLM.xcodeproj -o #{project_dir}/Targets --verbose")
  end

  desc "Setup all environments"
  lane :setup_all do
    environments = get_environments

    environments.each do |env|
      sh("bundle exec fastlane setup --env #{env}")
    end
  end

  desc "Build for pre-production testing and distribute via Firebase"
  desc "Requires environment to be set (e.g., --env computerBob)"
  lane :preprod do
    IPA_PREPROD_PATH = "#{ENV['BUILD_OUT_DIR']}/PREPROD"

    build(adhoc: true, output: IPA_PREPROD_PATH)
    
    firebase_app_distribution(
      app: ENV['FIREBASE_APP'],
      service_credentials_file: ENV['GOOGLE_CREDENTIALS'],
      ipa_path: "#{IPA_PREPROD_PATH}/#{ENV['SCHEME']}.ipa",
      release_notes: release_notes,
      groups: "qa, developers, resc-core, resc-clients"
    )
  end

  desc "Build for production and upload to App Store"
  desc "Requires environment to be set (e.g., --env computerBob)"
  lane :prod do
    IPA_PROD_PATH = "#{ENV['BUILD_OUT_DIR']}/PROD"

    build(adhoc: false, output: IPA_PROD_PATH)

    # upload_to_app_store(
    #   skip_metadata: true,
    #   skip_screenshots:
    #   true,
    #   ipa: "#{IPA_PREPROD_PATH}/#{ENV['APP_NAME']}.ipa"
    # )

    # dsym_zip(
    #   dsym_path: "#{IPA_PREPROD_PATH}",
    #   all: true
    # )

    # upload_symbols_to_crashlytics(
    #   gsp_path: "./#{ENV['SCHEME']}/Configuration/GoogleService/GoogleService-Info.plist"
    # )
  end

  desc "Build all environments"
  desc "#### Example:"
  desc "```\nfastlane build_all type:preprod\n```"
  desc "#### Options:"
  desc " * **type**: Either `preprod` or `prod`, determines which lane to run"
  lane :build_all do |params|
    valid_types = ["preprod", "prod"]
    build_type = params[:type]

    unless build_type
      UI.user_error!("Missing required parameter: type. Must be one of: #{valid_types.join(', ')}")
    end

    unless valid_types.include?(build_type)
      UI.user_error!("Invalid type: #{build_type}. Must be one of: #{valid_types.join(', ')}")
    end
    
    environments = get_environments

    environments.each do |env|
      case build_type
      when 'preprod'
        sh("bundle exec fastlane preprod --env #{env}")
      when 'prod'
        sh("bundle exec fastlane prod --env #{env}")
      end
    end
  end

  desc "Bump marketing version (minor or patch) for all targets"
  desc "#### Example:"
  desc "```\nfastlane bump_version type:patch\n```"
  desc "#### Options:"
  desc " * **type**: Either `minor` or `patch`. Major version is preserved"
  lane :bump_version do |options|
    bump_type = options[:type]
    valid_types = ["minor", "patch"]

    unless bump_type
      UI.user_error!("Missing required parameter: type. Must be one of: #{valid_types.join(', ')}")
    end

    unless valid_types.include?(bump_type)
      UI.user_error!("Invalid bump_type: #{bump_type}. Use 'minor' or 'patch'")
    end

    project_dir = File.dirname(File.expand_path('.'))
    project_path = "#{project_dir}/RLM.xcodeproj"
    UI.user_error!("No .xcodeproj found in root") unless project_path

    project = Xcodeproj::Project.open(project_path)
    UI.message("Opened project: #{project_path}")

    project.targets.each do |target|
      UI.message("Processing target: #{target.name}")

      target.build_configurations.each do |config|
        version = config.build_settings["MARKETING_VERSION"]
        next unless version

        major, minor, patch = version.split(".").map(&:to_i)

        case bump_type
        when "minor"
          minor += 1
          patch = 0
        when "patch"
          patch += 1
        end

        new_version = "#{major}.#{minor}.#{patch}"
        config.build_settings["MARKETING_VERSION"] = new_version

        UI.success("✔ Updated #{target.name} (#{config.name}) from #{version} to #{new_version}")
      end
    end

    project.save
  end

  private_lane :build do |params|
    IS_ADHOC = params[:adhoc]
    IPA_PATH = params[:output]
    CONFIGURATION = (IS_ADHOC ? "AdHoc" : "AppStore")
    EXPORT_METHOD = (IS_ADHOC ? "ad-hoc" : "app-store")
    INCLUDE_BITCODE = (IS_ADHOC ? false : true)

    PROVISIONING_PROFILES = {
      ENV['BUNDLE_IDENTIFIER'] => "#{ENV['PROVISIONING_PROFILE']} #{CONFIGURATION}"
    }
    
    if ENV['EXTENSIONS']
      EXTENSIONS = ENV['EXTENSIONS'].delete(' ').split(',')
      EXTENSIONS.each do |extension|
          extension_id = ENV['BUNDLE_IDENTIFIER'] + '.' + extension
          PROVISIONING_PROFILES[extension_id] = "#{ENV['PROVISIONING_PROFILE']} #{extension} #{CONFIGURATION}"
      end
    end

    clean_build_artifacts
    
    set_build_number

    setup

    build_app(
      scheme: ENV['SCHEME'],
      configuration: CONFIGURATION,
      silent: true,
      clean: true,
      export_method: EXPORT_METHOD,
      include_bitcode: INCLUDE_BITCODE,
      skip_profile_detection: true,
      export_options: {
          uploadSymbols: (IS_ADHOC ? false : true),
          provisioningProfiles: PROVISIONING_PROFILES,
          signingStyle: "manual"
      },
      output_directory: IPA_PATH,
      output_name: "#{ENV['SCHEME']}.ipa" 
    )
  end

  private_lane :set_build_number do
    project_dir = File.dirname(File.expand_path('.'))
    project_path = "#{project_dir}/RLM.xcodeproj"
    UI.user_error!("No .xcodeproj found") unless project_path

    target_name = ENV['SCHEME']
    UI.user_error!("SCHEME not set in ENV") unless target_name

    extension_name = "#{target_name}-NotificationServiceExtension"
    build_number = Time.now.strftime("%Y%m%d%H%M")
    
    UI.message("Setting build number #{build_number} for: #{target_name} and #{extension_name}")

    project = Xcodeproj::Project.open(project_path)

    # Find main target
    main_target = project.targets.find { |t| t.name == target_name }
    UI.user_error!("Target #{target_name} not found") unless main_target

    # Find extension target (if it exists)
    extension_target = project.targets.find { |t| t.name == extension_name }

    [main_target, extension_target].compact.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['CURRENT_PROJECT_VERSION'] = build_number
        UI.success("✔ Set build number for #{target.name} (#{config.name}) to #{build_number}")
      end
    end

    project.save
  end

  private_lane :get_environments do
    env_files = Dir[File.dirname('fastlane') + '/.env.*']

    environments = env_files.map do |path|
      File.basename(path).sub('.env.', '')
    end

    environments.reject! { |env| env == "default" }

    environments
  end

  desc "Generate Firebase release notes for environment"
  private_lane :release_notes do
    project_dir = File.dirname(File.expand_path('.'))
    scheme = ENV['SCHEME']
    features = File.read("#{project_dir}/Targets/#{scheme}/features.txt")

    changelog = <<~EOF
      ## Build Info

      - Branch: #{git_branch}
      - Scheme: #{scheme}

      ## Build Features

      #{features}
    EOF

    changelog
  end

  # error block is executed when an error occurs
  error do |lane, exception|
    puts "#{ENV['SCHEME']} encountered an error"
    puts "Output" + exception.to_s
  end
end
