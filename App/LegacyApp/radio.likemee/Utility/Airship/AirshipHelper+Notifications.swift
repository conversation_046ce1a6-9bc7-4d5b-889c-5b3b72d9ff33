//
//  AirshipHelper+Implementation.swift
//  radio.likemee
//
//  Created by <PERSON> on 10/12/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

#if USE_AIRSHIP
import UIKit
import Foundation
import AirshipCore

extension AirshipHelper {
    func application(
        _ application: UIApplication,
        didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
    ) {
        AppIntegration.application(
            application,
            didRegisterForRemoteNotificationsWithDeviceToken: deviceToken
        )
    }
    
    func application(
        _ application: UIApplication,
        didFailToRegisterForRemoteNotificationsWithError error: Error
    ) {
        AppIntegration.application(
            application,
            didFailToRegisterForRemoteNotificationsWithError: error
        )
    }
    
    @MainActor func application(
        _ application: UIApplication,
        didReceiveRemoteNotification userInfo: [AnyHashable: Any],
        fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
    ) {
        Util.debugLog("userInfo: \(userInfo)")
        AppIntegration.application(
            application,
            didReceiveRemoteNotification: userInfo,
            fetchCompletionHandler: completionHandler
        )
    }
    
    // UNUserNotificationCenterDelegate methods
    func userNotificationCenter(
        center: UNUserNotificationCenter,
        willPresentNotification notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (_ options: UNNotificationPresentationOptions) -> Void
    ) {
        Util.debugLog("userInfo: \(notification.request.content.userInfo)")
        AppIntegration.userNotificationCenter(
            center,
            willPresent: notification,
            withCompletionHandler: completionHandler
        )
    }
    
    func userNotificationCenter(
        center: UNUserNotificationCenter,
        didReceiveNotificationResponse response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        Util.debugLog("userInfo: \(response.notification.request.content.userInfo)")
        AppIntegration.userNotificationCenter(
            center,
            didReceive: response,
            withCompletionHandler: completionHandler
        )
    }
}

extension AirshipHelper: PushNotificationDelegate {
    func receivedBackgroundNotification(
        _ userInfo: [AnyHashable: Any],
        completionHandler: @escaping (UIBackgroundFetchResult) -> Void
    ) {
        // Background content-available notification
        Util.debugLog("Background content-available notification received!")
        completionHandler(.noData)
    }
    
    func receivedForegroundNotification(
        _ userInfo: [AnyHashable: Any],
        completionHandler: @escaping () -> Void
    ) {
        // Foreground notification
        Util.debugLog("Foreground notification received!")
        completionHandler()
    }
    
    func receivedNotificationResponse(
        _ notificationResponse: UNNotificationResponse,
        completionHandler: @escaping () -> Void
    ) {
        Util.debugLog(
            "Received notification response with action id \(notificationResponse.actionIdentifier)!"
        )
        
        let userInfo = notificationResponse.notification.request.content.userInfo
        for entry in userInfo {
            DispatchQueue.main.async {
                if let name = entry.key as? String,
                   Airship.actionRegistry.entry(name: name) != nil {
                    // The Airship will handle this action
                    return
                }
            }
        }
        
        if notificationResponse.actionIdentifier == "com.apple.UNNotificationDefaultActionIdentifier" {
            // default action is open home page
            PersistentNotificationCenter.persistentCenter.post(
                name: DeeplinkNotification.home,
                object: nil
            )
            return
        }
        
        completionHandler()
    }
}

extension AirshipHelper: DeepLinkDelegate {
    func receivedDeepLink(_ deepLink: URL) async {
        Util.debugLog("Will process deep link: '\(deepLink.absoluteString)'")
        UniversalLinks.processLink(deepLink)
    }
}
#else
import UserNotifications
import UIKit

extension AirshipHelper {
    func application(
        _ application: UIApplication,
        didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
    ) {}
    
    func application(
        _ application: UIApplication,
        didFailToRegisterForRemoteNotificationsWithError error: Error
    ) {}
    
    func userNotificationCenter(
        center: UNUserNotificationCenter,
        willPresentNotification notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (_ options: UNNotificationPresentationOptions) -> Void
    ) {
        completionHandler([.banner])
    }
}
#endif
