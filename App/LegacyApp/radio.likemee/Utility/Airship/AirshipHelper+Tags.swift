//
//  Airship+Tags.swift
//  radio.likemee
//
//  Created by Computer Rock on 11.10.24..
//  Copyright © 2024 Computer Rock. All rights reserved.
//

#if USE_AIRSHIP
import AirshipCore

extension AirshipHelper {
    func set(tags: [String], group: String) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Airship.channel.editTagGroups { editor in
            editor.set(tags, group: group)
        }
        
        Util.debugLog("tags set: '\(tags)'")
    }
    
    func add(tags: [String], group: String) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Airship.channel.editTagGroups { editor in
            editor.add(tags, group: group)
        }
        
        Util.debugLog("tags added: '\(tags)'")
    }
    
    func remove(tags: [String], group: String) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Airship.channel.editTagGroups { editor in
            editor.remove(tags, group: group)
        }
        
        Util.debugLog("tags removed: '\(tags)'")
    }
    
    func set(userTags: [String], group: String) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Airship.contact.editTagGroups { editor in
            editor.set(userTags, group: group)
        }
        
        Util.debugLog("user tags set: '\(userTags)'")
    }
    
    func add(userTags: [String], group: String) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Airship.contact.editTagGroups { editor in
            editor.add(userTags, group: group)
        }
        
        Util.debugLog("user tags added: '\(userTags)'")
    }
    
    func remove(userTags: [String], group: String) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Airship.contact.editTagGroups { editor in
            editor.remove(userTags, group: group)
        }
        
        Util.debugLog("user tags removed: '\(userTags)'")
    }
    
    func updateOptInTag(forTracking name: String, _ given: Bool) {
        if given {
            add(
                tags: ["opt_in_" + name],
                group: RLMSettings.airshipPrivacyGroup
            )
        } else {
            remove(
                tags: ["opt_in_" + name],
                group: RLMSettings.airshipPrivacyGroup
            )
        }
    }
    
    func applyMutations(profile: RlmDataProfile) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Task {
            let airshipID = await Airship.contact.namedUserID
            
            if let id = profile.id, id != airshipID {
                Airship.contact.identify(id)
            }
            
            let filledIn = updateAttributes(using: profile)
            updateTagGroups(isProfileComplete: filledIn == 10)
        }
    }
    
    private func updateAttributes(using profile: RlmDataProfile) -> Int {
        var filledIn = 0
        
        Airship.contact.editAttributes { editor in
            filledIn += setOrRemoveAttribute(
                value: profile.firstName,
                key: "first_name",
                editor: editor
            )
            filledIn += setOrRemoveAttribute(
                value: profile.lastName,
                key: "last_name",
                editor: editor
            )
            filledIn += setOrRemoveAttribute(
                value: profile.zip,
                key: "zip",
                editor: editor
            )
            filledIn += setOrRemoveAttribute(
                value: profile.country,
                key: "country",
                editor: editor
            )
            filledIn += setOrRemoveAttribute(
                value: profile.city,
                key: "city",
                editor: editor
            )
            filledIn += setOrRemoveAttribute(
                value: profile.address,
                key: "street",
                editor: editor
            )
            filledIn += setOrRemoveAttribute(
                value: profile.email,
                key: "email",
                editor: editor
            )
            filledIn += setOrRemoveAttribute(
                value: profile.gender,
                key: "gender",
                editor: editor
            )
            filledIn += setOrRemoveAttribute(
                date: profile.birthday,
                key: "birthday",
                editor: editor
            )
            filledIn += setOrRemoveAttribute(
                value: profile.phone,
                key: "phone",
                editor: editor
            )
        }
        
        return filledIn
    }
    
    private func setOrRemoveAttribute(
        value: String?,
        key: String,
        editor: AttributesEditor
    ) -> Int {
        let trimmedValue = value?.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard let trimmedValue, !trimmedValue.isEmpty else {
            editor.remove(key)
            return 0
        }
        
        editor.set(string: trimmedValue, attribute: key)
        
        return 1
    }
    
    private func setOrRemoveAttribute(
        date: String?,
        key: String,
        editor: AttributesEditor
    ) -> Int {
        let trimmedValue = date?.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard let trimmedValue, !trimmedValue.isEmpty else {
            editor.remove(key)
            return 0
        }
        
        let date = trimmedValue.toAirshipDate() ?? Date()
        editor.set(date: date, attribute: key)
        
        return 1
    }
    
    private func updateTagGroups(isProfileComplete: Bool) {
        Airship.contact.editTagGroups { editor in
            if isProfileComplete {
                editor.add(
                    ["profile_complete"],
                    group: RLMSettings.airshipAccountGroup
                )
            } else {
                editor.remove(
                    ["profile_complete"],
                    group: RLMSettings.airshipAccountGroup
                )
            }
        }
    }
}
#else
extension AirshipHelper {
    func set(tags: [String], group: String) {}
    
    func add(tags: [String], group: String) {}
    
    func remove(tags: [String], group: String) {}
    
    func updateOptInTag(forTracking name: String, _ given: Bool) {}
    
    func applyMutations(profile: RlmDataProfile) {}
}
#endif
