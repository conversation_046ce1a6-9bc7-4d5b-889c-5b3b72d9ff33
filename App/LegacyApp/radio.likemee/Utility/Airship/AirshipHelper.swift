//
//  AirshipHelper.swift
//  radio.likemee
//
//  Created by <PERSON> on 10/12/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import Foundation

#if USE_AIRSHIP
import AirshipCore
import AirshipAutomation

class AirshipHelper: NSObject {
    static let shared = AirshipHelper()
    
    var lastFavorites = [String]()
    
    private override init() {
        super.init()
    }
    
    @MainActor func takeOff() {
        guard RLMSettings.airshipEnabled else {
            Util.debugLog("Airship is DISABLED in the settings but it is included in the build with USE_AIRSHIP")
            return
        }
        
        if TrackAirship.shared.takeOffAlreadyCalled {
            return
        } else {
            TrackAirship.shared.takeOffAlreadyCalled = true
        }
        
        Util.debugLog("Airship is taking off!")
        
        let config = AirshipConfig()
        config.isAutomaticSetupEnabled = false
        config.site = RLMSettings.airshipSite == "EU" ? .eu : .us
        config.developmentAppKey = RLMSettings.airshipDevelopmentAppKey
        config.developmentAppSecret = RLMSettings.airshipDevelopmentAppSecret
        config.productionAppKey = RLMSettings.airshipProductionAppKey
        config.productionAppSecret = RLMSettings.airshipProductionAppSecret
        
        if TrackAirship.shared.trackingEnabled == false {
            config.enabledFeatures = []
        } else {
            config.enabledFeatures = [.all]
        }
        
        for url in RLMSettings.airshipURLAllowList.split(separator: ",") {
            config.urlAllowList.append(String(url))
        }
        
        for url in RLMSettings.airshipURLAllowListScopeOpenUrl.split(separator: ",") {
            config.urlAllowListScopeOpenURL.append(String(url))
        }
        
        for url in RLMSettings.airshipURLAllowListScopeJavaScriptInterface.split(separator: ",") {
            config.urlAllowListScopeJavaScriptInterface.append(String(url))
        }
        
#if DEBUG
        config.inProduction = false
        config.developmentLogLevel = Util.Loggers.airship ? .debug : .none
        Util.debugLog("Airship is in the development mode!")
#else
        config.inProduction = true
        Util.debugLog("Airship is in the production mode!")
#endif
        
        // --- start
        Airship.takeOff(config, launchOptions: nil)
        
        // Set delegates
        Airship.push.pushNotificationDelegate = self
        Airship.push.autobadgeEnabled = true
        Airship.deepLinkDelegate = self
        
        // pause automation initially
        InAppAutomation.shared.isPaused = true
        
        // register custom notification categories
        registerCategories()
    }
    
    @MainActor private func registerCategories() {
        Util.debugLog("registering categories")
        
        // Define actions
        let listen = UNNotificationAction(
            identifier: "listen_now",
            title: "notification_action_listen_now".localized(),
            options: [.foreground]
        )
        let participate = UNNotificationAction(
            identifier: "participate_now",
            title: "notification_action_participate_now".localized(),
            options: [.foreground]
        )
        let read = UNNotificationAction(
            identifier: "read_now",
            title: "notification_action_read_now".localized(),
            options: [.foreground]
        )
        let decline = UNNotificationAction(
            identifier: "no_thank_you",
            title: "notification_action_no_thank_you".localized(),
            options: []
        )
        // alarm action from previous app versions
        let openApp = UNNotificationAction(
            identifier: "OpenApp",
            title: "button_open_app".localized(),
            options: [.foreground]
        )
        
        // Define categories
        let categoryListen = UNNotificationCategory(
            identifier: "listen_now",
            actions: [
                listen,
                decline
            ],
            intentIdentifiers: [],
            hiddenPreviewsBodyPlaceholder: "notification_category_listen_now_hidden_placeholder".localized(),
            options: []
        )
        
        let categoryParticipate = UNNotificationCategory(
            identifier: "participate_now",
            actions: [
                participate,
                decline
            ],
            intentIdentifiers: [],
            hiddenPreviewsBodyPlaceholder: "notification_category_participate_now_hidden_placeholder".localized(),
            options: []
        )
        
        let categoryRead = UNNotificationCategory(
            identifier: "read_now",
            actions: [
                read,
                decline
            ],
            intentIdentifiers: [],
            hiddenPreviewsBodyPlaceholder: "notification_category_read_now_hidden_placeholder".localized(),
            options: []
        )
        
        let openAppCategory = UNNotificationCategory(
            identifier: "OpenAppCategory",
            actions: [openApp],
            intentIdentifiers: [],
            options: []
        )
        
        // Set the custom categories
        Airship.push.customCategories = [
            categoryListen,
            categoryParticipate,
            categoryRead,
            openAppCategory
        ]
    }
    
    func land() {
        Util.debugLog("Airship is landing!")
        TrackAirship.shared.takeOffAlreadyCalled = false
    }
    
    func airshipChannelID() -> String? {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return nil
        }
        
        guard let channelID = Airship.channel.identifier else {
            return nil
        }
        
        return channelID
    }
    
    func resetContact() {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Airship.contact.reset()
    }
    
    func resetBadge() {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Task {
            do {
                try await Airship.push.resetBadge()
            } catch {
                Util.debugLog("Airship failed to reset badge")
            }
        }
    }
    
    @MainActor
    func setInAppAutomation(paused: Bool) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        InAppAutomation.shared.isPaused = paused || (TrackAirship.shared.trackingEnabled == false)
    }
    
    func enablePushNotifications(_ enable: Bool) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Airship.push.userPushNotificationsEnabled = enable
        
        if enable {
            AirshipHelper.shared.add(
                tags: ["allow_push_notifications"],
                group: RLMSettings.airshipPrivacyGroup
            )
        } else {
            AirshipHelper.shared.remove(
                tags: ["allow_push_notifications"],
                group: RLMSettings.airshipPrivacyGroup
            )
        }
    }
    
    // sync flags in case of upgrade or crash that interrupts savung to UserDefaults
    func setNotifications() {
        guard TrackAirship.shared.trackingEnabled else {
            return
        }
        
        if UserDefaults.standard.bool(forKey: UserDefaultsKeys.askedForNotifications) {
            UNUserNotificationCenter.current().getNotificationSettings { (settings) in
                AirshipHelper.shared.enablePushNotifications(
                    settings.authorizationStatus != .denied
                )
            }
        }
    }
}

// Helpers
extension AirshipHelper {
    func mergeFavorites(_ newList: [ResponseObjectMainConfigStartPageElement]? = []) {
        var tags = [String]()
        
        for element in newList ?? [] {
            if let _ = tags.first(where: { (tag) -> Bool in
                if let id = element.mc_flow_id ?? element.uuid {
                    return tag == "\(element.elementType == ElementType.podcastSeries ? "podcast" : "stream")_\(id)"
                }
                return false
            }) {
                continue
            } else {
                if let id = element.mc_flow_id ?? element.uuid  {
                    tags.append("\(element.elementType == ElementType.podcastSeries ? "podcast" : "stream")_\(id)")
                }
            }
            
            if let _ = tags.first(where: { (tag) -> Bool in
                let elName = "\(element.elementType == ElementType.podcastSeries ? "podcast" : "stream")_\(element.element_name ?? "")"
                return tag == elName
            }) {
                continue
            } else {
                if let names = element.element_name {
                    tags.append("\(element.elementType == ElementType.podcastSeries ? "podcast" : "stream")_\(names)")
                }
            }
        }
        
        if tags.containsSameElements(as: self.lastFavorites) == false {
            set(userTags: tags, group: RLMSettings.airshipFavouritesGroup)
            self.lastFavorites = tags
        }
    }
}

// Privacy Manager
extension AirshipHelper {
    func isAnyFeatureEnabled() -> Bool {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return false
        }
        
        return Airship.privacyManager.isAnyFeatureEnabled()
    }
    
    func enableAllFeatures() {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Airship.privacyManager.enableFeatures(.all)
    }
    
    func disableAllFeatures() {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Airship.privacyManager.disableFeatures(.all)
    }
}

// Analytics
extension AirshipHelper {
    func trackScreen(_ screenName: String) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        Task { @MainActor in
            Airship.analytics.trackScreen(screenName)
        }
    }
    
    func send(eventName: String, value: NSNumber? = nil, properties: [String: Any]? = nil) {
        guard TrackAirship.shared.trackingEnabled, Airship.isFlying else {
            return
        }
        
        let event: CustomEvent = CustomEvent(name: eventName)
        var logString = "🛤 event: '\(event.eventName ?? "")'"
        
        if let value {
            event.eventValue = value
            logString += " value: \(value)"
        }
        if let properties = properties {
            event.properties = properties
            logString += "  properties: '\(properties)'"
        }
        Util.debugLog(logString)
        
        // Record the event in analytics
        event.track()
    }
}
#else
class AirshipHelper: NSObject {
    static let shared = AirshipHelper()
    
    @MainActor func takeOff() {}
    
    func resetBadge() {}
    
    func resetContact() {}
    
    @MainActor func setInAppAutomation(paused: Bool) {}
    
    func enablePushNotifications(_ enable: Bool) {}
    
    func airshipChannelID() -> String? {
        return nil
    }
    
    func mergeFavorites(_ newList: [ResponseObjectMainConfigStartPageElement]? = []) {}
}
#endif
