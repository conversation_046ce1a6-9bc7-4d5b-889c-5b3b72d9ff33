//
//  QuantumCastAd.swift
//  RLM
//
//  Created by Computer Rock on 30. 5. 2025..
//

struct QuantumCastAd: Codable {
    enum PayloadType: String, Codable {
        case companionAd = "companionad"
    }
    
    struct CompanionClickThrough: Codable {
        var URI: String?
    }
    
    struct CompanionClickTracking: Codable {
        var URI: String?
    }
    
    struct TrackingEvent: Codable {
        var URI: String?
    }
    
    struct StaticResource: Codable {
        var URI: String?
    }
    
    struct CompanionAd: Codable {
        var ID: String?
        var AdLength: Int? {
            if let val = self.duration_ms {
                return val / 1000
            }
            return nil
        }
        var duration_ms: Int?
        var CompanionClickThrough: CompanionClickThrough? = nil
        var CompanionClickTracking: [CompanionClickTracking]? = []
        var TrackingEvents: [TrackingEvent]? = []
        var StaticResource: StaticResource? = nil

        enum CodingKeys: String, CodingKey {
            case ID, duration_ms
            case CompanionClickThrough, CompanionClickTracking
            case TrackingEvents, StaticResource
        }

        init(from decoder: any Decoder) throws {
            let container: KeyedDecodingContainer<CodingKeys> = try decoder.container(
                keyedBy: CodingKeys.self
            )
            
            self.ID = try container.decodeIfPresent(
                String.self,
                forKey: CodingKeys.ID
            )
            self.duration_ms = try container.decodeIfPresent(
                Int.self,
                forKey: CodingKeys.duration_ms
            )
            self.CompanionClickThrough = try container.decodeIfPresent(
                QuantumCastAd.CompanionClickThrough.self,
                forKey: CodingKeys.CompanionClickThrough
            )
            self.CompanionClickTracking = try container.decodeIfPresent(
                [QuantumCastAd.CompanionClickTracking].self,
                forKey: CodingKeys.CompanionClickTracking
            )

            if let trackingEventsArray = try? container.decode(
                [TrackingEvent].self,
                forKey: .TrackingEvents
            ) {
                self.TrackingEvents = trackingEventsArray
            } else if let singleTrackingEvent = try? container.decode(
                TrackingEvent.self,
                forKey: .TrackingEvents
            ) {
                self.TrackingEvents = [singleTrackingEvent]
            } else {
                self.TrackingEvents = []
            }

            self.StaticResource = try container
                .decodeIfPresent(
                    QuantumCastAd.StaticResource.self,
                    forKey: CodingKeys.StaticResource
                )
        }
    }
    
    var payload_type: PayloadType?
    var companion_ads: [CompanionAd] = []
}

