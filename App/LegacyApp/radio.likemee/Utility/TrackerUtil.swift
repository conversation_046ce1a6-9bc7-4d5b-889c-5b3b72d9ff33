//
//  TrackerUtil.swift
//  radio.likemee
//
//  Created by <PERSON> on 5/29/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

import Foundation

class TrackerUtil: NSObject {
    var appOpenTracked = false
    
    func setConsentAll() {
        Util.debugLog("🛤 CONSENT ALL")
        
        TrackOnePlusX.shared.isTrackingEnabled = true
        TrackFirebaseAnalytics.shared().isTrackingEnabled = true
        TrackFirebaseAnalytics.shared().isTrackingEnabledCrashlytics = true
        TrackSqs.shared().isTrackingEnabled = true
        VastService.shared.isTrackingEnabled = true
        TrackAppsFlyer.shared().isTrackingEnabled = true
        TrackAirship.shared.trackingEnabled = true
    }
    
    func removeConsentAll() {
        Util.debugLog("🛤 CONSENT ALL")
        
        TrackOnePlusX.shared.isTrackingEnabled = false
        TrackFirebaseAnalytics.shared().isTrackingEnabled = false
        TrackFirebaseAnalytics.shared().isTrackingEnabledCrashlytics = false
        TrackSqs.shared().isTrackingEnabled = false
        VastService.shared.isTrackingEnabled = false
        TrackAppsFlyer.shared().isTrackingEnabled = false
        TrackAirship.shared.trackingEnabled = false
    }
    
    func removeConsentAllExceptAirship() {
        Util.debugLog("🛤 CONSENT ALL")
        
        TrackOnePlusX.shared.isTrackingEnabled = false
        TrackFirebaseAnalytics.shared().isTrackingEnabled = false
        TrackFirebaseAnalytics.shared().isTrackingEnabledCrashlytics = false
        TrackSqs.shared().isTrackingEnabled = false
        VastService.shared.isTrackingEnabled = false
        TrackAppsFlyer.shared().isTrackingEnabled = false
        TrackAirship.shared.trackingEnabled = true
    }
    
    func setConsentOnAppRestart() {
        Util.debugLog("🛤 CONSENT SOME")
        
        let onePlusX = TrackOnePlusX.shared.isTrackingEnabled
        let firebase = TrackFirebaseAnalytics.shared().isTrackingEnabled
        let crashlytics = TrackFirebaseAnalytics.shared().isTrackingEnabledCrashlytics
        let adswizz = VastService.shared.isTrackingEnabled
        let sqs = TrackSqs.shared().isTrackingEnabled
        let airship = TrackAirship.shared.trackingEnabled
        let appsFlyer = TrackAppsFlyer.shared().isTrackingEnabled
        
        TrackOnePlusX.shared.setConsent(given: onePlusX, isAppStart: true)
        TrackFirebaseAnalytics.shared().setConsent(given: firebase, isAppStart: true)
        TrackFirebaseAnalytics.shared().setConsentCrashlytics(given: crashlytics, isAppStart: true)
        TrackSqs.shared().setConsent(given: sqs, isAppStart: true)
        VastService.shared.setConsent(given: adswizz, isAppStart: true)
        TrackAppsFlyer.shared().setConsent(given: appsFlyer, isAppStart: true)
        TrackAirship.shared.setConsent(given: airship)
        
#if USE_USERCENTRICS
        UserCentricsRepository.shared.checkConsents()
#endif
    }
    
    /// Updating the opt_in tags will only work if Airship tracking is ENABLED
    func updatePrivacyTags() {
        guard TrackAirship.shared.trackingEnabled else {
            return
        }
        
        var tagsToAdd = [String]()
        var tagsToRemove = [String]()
        
        if TrackOnePlusX.shared.isTrackingEnabled {
            tagsToAdd.append("opt_in_1plusx")
        } else {
            tagsToRemove.append("opt_in_1plusx")
        }
        
        if TrackAppsFlyer.shared().isTrackingEnabled {
            tagsToAdd.append("opt_in_appsflyer")
        } else {
            tagsToRemove.append("opt_in_appsflyer")
        }
        
        if TrackFirebaseAnalytics.shared().isTrackingEnabled {
            tagsToAdd.append("opt_in_google_analytics")
        } else {
            tagsToRemove.append("opt_in_google_analytics")
        }
        
        if TrackFirebaseAnalytics.shared().isTrackingEnabledCrashlytics {
            tagsToAdd.append("opt_in_google_crashlytics")
        } else {
            tagsToRemove.append("opt_in_google_crashlytics")
        }
        
        if VastService.shared.isTrackingEnabled {
            tagsToAdd.append("opt_in_adswizz")
        } else {
            tagsToRemove.append("opt_in_adswizz")
        }
        
        if TrackAirship.shared.trackingEnabled && TrackSqs.shared().isTrackingEnabled {
            tagsToAdd.append("opt_in_personalisierte_inhalte")
            tagsToAdd.append("allow_push_notifications")
        } else {
            tagsToRemove.append("opt_in_personalisierte_inhalte")
            tagsToRemove.append("allow_push_notifications")
        }
        
        AirshipHelper.shared.add(
            tags: tagsToAdd,
            group: RLMSettings.airshipPrivacyGroup
        )
        AirshipHelper.shared.remove(
            tags: tagsToRemove,
            group: RLMSettings.airshipPrivacyGroup
        )
    }
    
    func onDidFinishLaunching() {
        TrackFirebaseAnalytics.shared().onDidFinishLaunching()
        TrackOnePlusX.shared.onDidFinishLaunching()
        TrackSqs.shared().onDidFinishLaunching()
        TrackAirship.shared.onDidFinishLaunching()
        TrackAppsFlyer.shared().onDidFinishLaunching()
    }
    
    func onAppOpen() {
        TrackFirebaseAnalytics.shared().onAppOpen()
        TrackOnePlusX.shared.onAppOpen()
        TrackSqs.shared().onAppOpen()
        TrackAirship.shared.onAppOpen()
        TrackAppsFlyer.shared().onAppOpen()
    }
    
    static let shared: TrackerUtil = {
        let instance = TrackerUtil()
        return instance
    }()
    
    func makeSQSLabel(
        series: RlmDataPodcast.Serie?,
        episode: RlmDataPodcast.Episode?
    ) -> String {
        return "\(String(describing: episode?.number ?? -1)) - \(episode?.title ?? "ERR")#####\(series?.title ?? "ERR")"
    }
    
    // MARK: - Tracking functions
    @MainActor func trackScreen(_ screenName: String) {
        TrackAirship.shared.trackScreen(screenName)
    }

#if USE_ALARMS
    func trackAlarmSettingFor(_ alarm: Alarm) {
        let channelFound = DataManager.shared.getChannelList()?.filter {
            $0.mc_flow_id == alarm.radioStreamId
        }.first
        let channelname = channelFound?.element_name ?? ""

        TrackerUtil.shared.trackAlarmSettingFor(
            channel: channelname,
            content: alarm.alarmContentType,
            isPersonalized: !alarm.personalizedName.isEmpty,
            isAlarmAradio: alarm.alarmType == Config.alarmTypeRadio,
            at: alarm.dateMark ?? "ERROR"
        )
    }

    private func trackAlarmSettingFor(
        channel: String,
        content: [String],
        isPersonalized: Bool,
        isAlarmAradio: Bool,
        at: String
    ) {
        let roundedTime = roundAlarmMinutesFor(alarmTime: at)
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.alarm.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.specify.rawValue,
                "daytime_start": roundedTime
            ]
        )
        
        TrackSqs.shared().send(
            category: "alarm",
            action: "set",
            label: roundedTime
        )
        
        TrackAirship.shared.send(
            event: "set_alarm",
            properties: [
                "alarm_type": isAlarmAradio ? "radio" : "sound",
                "alarm_personalized": isPersonalized,
                "alarm_time": roundedTime
            ]
        )
    }
#endif

    func trackStartedPlaying(stream: ResponseObjectMainConfigStartPageElement) {
        guard let streamName = stream.element_name else {
            Util.debugLog("🛤 Problem?")
            return
        }
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.stream.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.play.rawValue,
                "media_serie": streamName
            ]
        )
        
        if let mediaID = stream.element_id, let stationID = stream.mc_flow_id {
            TrackAppsFlyer.shared().send(
                action: RLMSettings.appsFlyerStartStream,
                parameters: [
                    "stream_name": streamName,
                    "mediaId": mediaID,
                    "stationId": stationID
                ]
            )
        }
        
        TrackSqs.shared().send(
            category: "stream",
            action: "play",
            label: streamName
        )
        
        TrackOnePlusX.shared.sendStreamPlay(
            forElement: stream,
            withMinutes: 0
        )
        
        TrackAirship.shared.send(
            event: "start_stream",
            properties: [
                "stream_name": streamName,
                "stream_id": stream.mc_flow_id ?? ""
            ]
        )
    }
    
    func trackStoppedPlaying(stream: ResponseObjectMainConfigStartPageElement?) {
        guard let streamName = stream?.element_name else {
            Util.debugLog("🛤 Problem?")
            return
        }
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.stream.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.stop.rawValue,
                "media_serie": streamName
            ]
        )
        
        TrackSqs.shared().send(
            category: "stream",
            action: "stop",
            label: streamName
        )
    }
    
    func trackStartedPlaying(
        element: ResponseObjectMainConfigStartPageElement?,
        aod: String?,
        from: String?
    ) {
        let aodName = aod ?? ""
        let fullName = aodName + "_" + (from?.uppercased() ?? "")
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.aod.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.play.rawValue,
                "media_serie": element?.element_name ?? ""
            ]
        )
        
        TrackSqs.shared().send(
            category: "audio",
            action: "play",
            label: fullName
        )
        
        if let element = element {
            TrackOnePlusX.shared.sendAodPlay(forElement: element)
            
            TrackAirship.shared.send(
                event: "listen_aod",
                properties: [
                    "aod_name": element.element_name ?? "",
                    "aod_type": element.element_tag ?? ""
                ]
            )
        }
    }
    
    func trackTimePlayedFor(stream: String?, duration: Int) {
        guard let streamName = stream, streamName != "" else {
            Util.debugLog("🛤 Problem?")
            return
        }
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.stream.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.listen.rawValue,
                "media_serie": streamName,
                "duration": String(duration)
            ]
        )
        
        if duration > 59 {
            TrackSqs.shared().send(
                category: streamName,
                action: "listen",
                label: String(duration)
            )
        }
    }
    
    func trackStreamListen(
        stream: ResponseObjectMainConfigStartPageElement?,
        minutes: Int
    ) {
        guard let streamName = stream?.element_name else {
            Util.debugLog("Failed! No stream name!")
            return
        }
        
        if Config.appsFlyerMinutesForTrackingValues.contains(minutes) {
            TrackAppsFlyer.shared().send(
                action: "\(RLMSettings.appsFlyerListenStream)_\(minutes)",
                parameters: [
                    "stream_name": streamName
                ]
            )
        }
        
        TrackAirship.shared.send(
            event: "listen_stream",
            value: NSNumber(value: minutes),
            properties: [
                "stream_name": streamName,
                "stream_id": stream?.mc_flow_id ?? ""
            ]
        )
        
        if minutes % 10 == 0 && minutes != 0 {
            TrackOnePlusX.shared.sendStreamPlay(
                forElement: stream,
                withMinutes: minutes
            )
        }
    }
    
    func trackChannel(
        _ channel: ResponseObjectMainConfigStartPageElement?,
        favorited: Bool
    ) {
        guard let channel_name = channel?.element_name,
              channel_name != "" else {
            Util.debugLog("🛤 Problem?")
            return
        }
        
        TrackSqs.shared().send(
            category: channel_name,
            action: favorited ? "followed": "unfollowed",
            label: ""
        )
        
        let user_stream_favorite_name = channel?.element_name ?? ""
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.stream.rawValue,
                "media_event": favorited ? TrackFirebaseAnalytics.MediaEvent.follow.rawValue: TrackFirebaseAnalytics.MediaEvent.unfollow.rawValue,
                "media_serie": channel?.element_name ?? ""
            ]
        )
        
        TrackAirship.shared.send(
            event: favorited ? "follow_stream": "unfollow_stream",
            properties: [
                "stream_name": user_stream_favorite_name,
                "stream_id": channel?.mc_flow_id ?? ""
            ]
        )
        
        TrackAirship.shared.favorite(
            id: channel?.mc_flow_id,
            title: user_stream_favorite_name,
            followed: favorited,
            isPodcast: false
        )
    }
    
    func trackSleepWith(element: SleepAidEntryObject) {
        if element.type == "podcast" {
            TrackAirship.shared.send(
                event: "start_sleep_timer",
                properties: [
                    "timer_type": "podcast",
                    "podcast_name": element.rc_element_name ?? "",
                    "podcast_id": element.rc_element_id ?? "",
                    "episode_name": element.title ?? "",
                    "episode_id": element.uid ?? "",
                    "stream_name": "",
                    "stream_id": ""
                ]
            )
        } else if element.type == ElementType.Stream.rawValue {
            TrackAirship.shared.send(
                event: "start_sleep_timer",
                properties: [
                    "timer_type": "stream",
                    "podcast_name": "",
                    "podcast_id": "",
                    "episode_name": "",
                    "episode_id": "",
                    "stream_name": element.rc_element_name ?? "",
                    "stream_id": element.mc_flow_id ?? ""
                ]
            )
        }
    }
    
    func trackShared(channel: String) {
        if channel == "" { return }
    }
    
    func trackMessengerMessageSentWith(subject: String) {
        TrackSqs.shared().send(
            category: "messenger",
            action: "send",
            label: subject
        )
    }
    
    func trackChatStart(name: String, tag: String?) {
        TrackAirship.shared.send(
            event: "chat_start",
            properties: [
                "chat_name": name,
                "chat_tag": tag ?? ""
            ]
        )
    }
    
    func trackLoggedInEmail() {
        TrackAppsFlyer.shared().send(action: RLMSettings.appsFlyerLogin)
        
        TrackSqs.shared().send(
            category: "login",
            action: "login",
            label: "login"
        )
        
        TrackOnePlusX.shared.sendLogin()
        
        TrackFirebaseAnalytics.shared().send(
            event: "login",
            parameters: [
                "method": "registered"
            ]
        )
        
        TrackAirship.shared.send(
            event: "log_in",
            properties: [
                "login_provider": "userpool"
            ]
        )
        
        TrackAirship.shared.setLoggedInTags()
    }
    
    func trackAccessTokenRefreshed() {
        TrackAirship.shared.send(
            event: "log_in_refresh",
            properties: [:]
        )
        
        TrackAirship.shared.setLoggedInTags()
    }
    
    func trackCheckIn() {
        TrackAirship.shared.setLoggedInTags()
    }
    
    func trackContinuedWithoutLogIn() {
        TrackSqs.shared().send(
            category: "login",
            action: "login",
            label: "no login"
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "no_login"
        )
    }
    
    func trackViewLogin() {
        TrackAirship.shared.send(
            event: "view_login",
            properties: [:]
        )
    }
    
    func trackStartRegistration() {
        TrackAirship.shared.send(
            event: "start_registration",
            properties: [
                "login_provider": "userpool"
            ]
        )
    }
    
    func trackRegistered() {
        TrackSqs.shared().send(
            category: "login",
            action: "register",
            label: "yes"
        )
        
        TrackOnePlusX.shared.sendLogin()
        
        TrackFirebaseAnalytics.shared().send(
            event: "sign_up"
        )
        
        AirshipHelper.shared.add(
            tags: ["registration_started"],
            group: RLMSettings.airshipAccountGroup
        )
    }
    
    func trackVerified() {
        TrackAppsFlyer.shared().send(action: RLMSettings.appsFlyerRegistration)
        
        TrackFirebaseAnalytics.shared().send(
            event: "confirm_register"
        )
        
        TrackAirship.shared.send(
            event: "complete_registration",
            properties: [
                "login_provider": "userpool"
            ]
        )
        
        AirshipHelper.shared.add(
            tags: ["registration_completed"],
            group: RLMSettings.airshipAccountGroup
        )
    }
    
    func trackUpdatedProfile() {
        TrackOnePlusX.shared.sendLogin()
        
        TrackAirship.shared.send(
            event: "save_profile",
            properties: [
                "login_provider": "userpool"
            ]
        )
    }
    
    func trackPodcastSeriesViewed(series: RlmDataPodcast.Serie?) {
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.view.rawValue,
                "media_serie": series?.title ?? ""
            ]
        )
        
        TrackSqs.shared().send(
            category: "podcast_series",
            action: "viewed",
            label: series?.title ?? ""
        )
        
        TrackAirship.shared.send(
            event: "click_podcast_series",
            properties: [
                "podcast_name": series?.title ?? "",
                "podcast_id": series?.uuid ?? ""
            ]
        )
    }
    
    func trackPodcastSeriesInfoOpened(seriesName: String?) {
        TrackSqs.shared().send(
            category: "podcast_series",
            action: "info_viewed",
            label: seriesName ?? ""
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.inform.rawValue,
                "media_serie": seriesName ?? ""
            ]
        )
    }
    
    func trackPodcastSeriesFollowUn(series: RlmDataPodcast.Serie?, followed: Bool) {
        TrackSqs.shared().send(
            category: "podcast_series",
            action: followed ? "followed": "unfollowed",
            label: series?.title ?? ""
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": followed ? TrackFirebaseAnalytics.MediaEvent.follow.rawValue: TrackFirebaseAnalytics.MediaEvent.unfollow.rawValue,
                "media_serie": series?.title ?? ""
            ]
        )
        
        TrackAirship.shared.send(
            event: followed ? "follow_podcast": "unfollow_podcast",
            properties: [
                "podcast_name": series?.title ?? "",
                "podcast_id": series?.uuid ?? ""
            ]
        )
        
        TrackAirship.shared.favorite(
            id: series?.uuid,
            title: series?.title,
            followed: followed,
            isPodcast: true
        )
    }
    
    func trackPodcastEpisodeStarted(
        series: RlmDataPodcast.Serie?,
        episode: RlmDataPodcast.Episode?
    ) {
        TrackSqs.shared().send(
            category: "podcast_episode",
            action: "episode_started",
            label: makeSQSLabel(series: series, episode: episode)
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.play.rawValue,
                "media_serie": series?.title ?? "",
                "media_track": episode?.title ?? ""
            ]
        )
        
        TrackOnePlusX.shared.sendPodcastPlay(
            forSerie: series,
            episode: episode
        )
        
        TrackAppsFlyer.shared().send(action: RLMSettings.appsFlyerStartPodcast)
        
        TrackAirship.shared.send(
            event: "start_podcast",
            properties: [
                "podcast_name": series?.title ?? "",
                "podcast_id": series?.uuid ?? "",
                "episode_name": episode?.title ?? "",
                "episode_id": episode?.uuid ?? ""
            ]
        )
    }
    
    func trackPodcastEpisodeDownloadStarted(_ episode: RlmDataPodcast.Episode) {
        let series = RlmDataPodcast.shared.library.getSerie(withEpisode: episode)
        
        TrackAirship.shared.send(
            event: "download_podcast",
            properties: [
                "podcast_name": series?.title ?? "",
                "podcast_id": series?.uuid ?? "",
                "episode_name": episode.title ?? "",
                "episode_id": episode.uuid ?? ""
            ]
        )
    }
    
    func trackPodcastEpisodeListenedToFirstQuarter(
        series: RlmDataPodcast.Serie?,
        episode: RlmDataPodcast.Episode?
    ) {
        guard let uuid = episode?.uuid else {
            return
        }
        
        let progressRecorded = PodcastManager.sharedInstance.podcastEpisodesProgressesPrecentages[uuid] ?? 0.0
        if progressRecorded >= 0.25 {
            return
        }
        
        PodcastManager.sharedInstance.savePodcastProgressPercentage(0.25, uuid: uuid)
        NotificationCenter.default.post(
            name: Notifications.episodeProgressUpdated,
            object: nil,
            userInfo: [
                "episodeId": uuid,
                "progress": 0.25
            ]
        )
        
        TrackSqs.shared().send(
            category: "podcast_episode",
            action: "listened_1st_quartile",
            label: makeSQSLabel(series: series, episode: episode)
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.listened_1st_quartile.rawValue,
                "media_serie": series?.title ?? "",
                "media_track": episode?.title ?? ""
            ]
        )
        
        TrackAirship.shared.send(
            event: "listen_podcast",
            value: 25,
            properties: [
                "podcast_name": series?.title ?? "",
                "podcast_id": series?.uuid ?? "",
                "episode_name": episode?.title ?? "",
                "episode_id": episode?.uuid ?? ""
            ]
        )
    }
    
    func trackPodcastEpisodeListenedToSecondQuarter(
        series: RlmDataPodcast.Serie?,
        episode: RlmDataPodcast.Episode?
    ) {
        guard let uuid = episode?.uuid else {
            return
        }
        
        let progressRecorded = PodcastManager.sharedInstance.podcastEpisodesProgressesPrecentages[uuid] ?? 0.0
        if progressRecorded >= 0.50 {
            return
        }
        
        PodcastManager.sharedInstance.savePodcastProgressPercentage(0.50, uuid: uuid)
        NotificationCenter.default.post(
            name: Notifications.episodeProgressUpdated,
            object: nil,
            userInfo: [
                "episodeId": uuid,
                "progress": 0.50
            ]
        )
        
        TrackSqs.shared().send(
            category: "podcast_episode",
            action: "listened_2nd_quartile",
            label: makeSQSLabel(series: series, episode: episode)
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.listened_2nd_quartile.rawValue,
                "media_serie": series?.title ?? "",
                "media_track": episode?.title ?? ""
            ]
        )
        
        TrackAirship.shared.send(
            event: "listen_podcast",
            value: 50,
            properties: [
                "podcast_name": series?.title ?? "",
                "podcast_id": series?.uuid ?? "",
                "episode_name": episode?.title ?? "",
                "episode_id": episode?.uuid ?? ""
            ]
        )
    }
    
    func trackPodcastEpisodeListenedToThirdQuarter(
        series: RlmDataPodcast.Serie?,
        episode: RlmDataPodcast.Episode?
    ) {
        guard let uuid = episode?.uuid else {
            return
        }
        
        let progressRecorded = PodcastManager.sharedInstance.podcastEpisodesProgressesPrecentages[uuid] ?? 0.0
        if progressRecorded >= 0.75 {
            return
        }
        
        PodcastManager.sharedInstance.savePodcastProgressPercentage(0.75, uuid: uuid)
        NotificationCenter.default.post(
            name: Notifications.episodeProgressUpdated,
            object: nil,
            userInfo: [
                "episodeId": uuid,
                "progress": 0.75
            ]
        )
        
        TrackSqs.shared().send(
            category: "podcast_episode",
            action: "listened_3rd_quartile",
            label: makeSQSLabel(series: series, episode: episode)
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.listened_3rd_quartile.rawValue,
                "media_serie": series?.title ?? "",
                "media_track": episode?.title ?? ""
            ]
        )
        
        TrackAirship.shared.send(
            event: "listen_podcast",
            value: 75,
            properties: [
                "podcast_name": series?.title ?? "",
                "podcast_id": series?.uuid ?? "",
                "episode_name": episode?.title ?? "",
                "episode_id": episode?.uuid ?? ""
            ]
        )
    }
    
    func trackPodcastEpisodeFinished(series: RlmDataPodcast.Serie?, episode: RlmDataPodcast.Episode?) {
        guard let uuid = episode?.uuid else {
            return
        }
        
        let progressRecorded = PodcastManager.sharedInstance.podcastEpisodesProgressesPrecentages[uuid] ?? 0.0
        if progressRecorded >= 1.00 {
            return
        }
        
        PodcastManager.sharedInstance.savePodcastProgressPercentage(1.00, uuid: uuid)
        NotificationCenter.default.post(
            name: Notifications.episodeProgressUpdated,
            object: nil,
            userInfo: [
                "episodeId": uuid,
                "progress": 1.00
            ]
        )
        
        TrackSqs.shared().send(
            category: "podcast_episode",
            action: "listened_4th_quartile",
            label: makeSQSLabel(series: series, episode: episode)
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.listened_4th_quartile.rawValue,
                "media_serie": series?.title ?? "",
                "media_track": episode?.title ?? ""
            ]
        )
        
        TrackAirship.shared.send(
            event: "listen_podcast",
            value: 100,
            properties: [
                "podcast_name": series?.title ?? "",
                "podcast_id": series?.uuid ?? "",
                "episode_name": episode?.title ?? "",
                "episode_id": episode?.uuid ?? ""
            ]
        )
    }
    
    func trackPodcastEpisodeMarkedAsListened(
        series: RlmDataPodcast.Serie?,
        episode: RlmDataPodcast.Episode?
    ) {
        TrackSqs.shared().send(
            category: "podcast_episode",
            action: "marked_listened",
            label: makeSQSLabel(series: series, episode: episode)
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.marked_listened.rawValue,
                "media_serie": series?.title ?? "",
                "media_track": episode?.title ?? ""
            ]
        )
        
        TrackAirship.shared.send(
            event: "mark_listened_podcast",
            properties: [
                "podcast_name": series?.title ?? "",
                "podcast_id": series?.uuid ?? "",
                "episode_name": episode?.title ?? "",
                "episode_id": episode?.uuid ?? ""
            ]
        )
    }
    
    func trackPodcastEpisodeDownloaded(
        series: RlmDataPodcast.Serie?,
        episode: RlmDataPodcast.Episode?
    ) {
        TrackSqs.shared().send(
            category: "podcast_episode",
            action: "downloaded",
            label: makeSQSLabel(series: series, episode: episode)
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.downloaded.rawValue,
                "media_serie": series?.title ?? "",
                "media_track": episode?.title ?? ""
            ]
        )
    }
    
    func trackPodcastEpisodeInfoViewed(
        series: RlmDataPodcast.Serie?,
        episode: RlmDataPodcast.Episode?
    ) {
        TrackSqs.shared().send(
            category: "podcast_episode",
            action: "info_viewed",
            label: makeSQSLabel(series: series, episode: episode)
        )
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.podcast.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.inform.rawValue,
                "media_serie": series?.title ?? "",
                "media_track": episode?.title ?? ""
            ]
        )
        
        TrackAirship.shared.send(
            event: "click_podcast_episode_details",
            properties: [
                "podcast_name": series?.title ?? "",
                "podcast_id": series?.uuid ?? "",
                "episode_name": episode?.title ?? "",
                "episode_id": episode?.uuid ?? ""
            ]
        )
    }
    
    func roundAlarmMinutesFor(alarmTime: String) -> String {
        var roundedString = "ERR"
        let arrayTime = alarmTime.components(separatedBy: ":")
        
        var shouldIncrementHour = false
        var mins: Int = 0
        var hrs: Int = 0
        
        if let minutes = Int(arrayTime[1]) {
            var roundedMinutes = (minutes + 4) / 10
            
            if roundedMinutes >= 6 {
                shouldIncrementHour = true
                roundedMinutes = 0
            }
            mins = roundedMinutes * 10
        }
        
        if let hours = Int(arrayTime[0]) {
            hrs = hours
            
            if shouldIncrementHour {
                var roundedHours = hours + 1
                if roundedHours >= 24 {
                    roundedHours = 0
                }
                hrs = roundedHours
            }
        }
        roundedString = String(format: "%02d:%02d", hrs, mins)
        return roundedString
    }
    
    func trackTapToWebView(element: ResponseObjectMainConfigStartPageElement?) {
        guard let element = element else {
            return
        }
        
        let name = element.element_name ?? ""
        let tag = element.element_tag ?? ""
        
        TrackFirebaseAnalytics.shared().send(
            event: "media_event",
            parameters: [
                "media_type": TrackFirebaseAnalytics.MediaType.webview.rawValue,
                "media_event": TrackFirebaseAnalytics.MediaEvent.view.rawValue,
                "media_serie": name
            ]
        )
        TrackAirship.shared.send(
            event: "click_webview",
            properties: [
                "webview_name": name,
                "webview_tag": tag
            ]
        )
    }
    
    func trackStreamQualityChanged(from: StreamQuality, to: StreamQuality) {
        TrackAirship.shared.send(
            event: "change_stream_quality",
            properties: [
                "from": from.toTrackingString(),
                "to": to.toTrackingString()
            ]
        )
    }
    
    func trackConnectedToExternalDisplay() {
        TrackAirship.shared.send(
            event: "use_in_car",
            properties: [:]
        )
    }
}
