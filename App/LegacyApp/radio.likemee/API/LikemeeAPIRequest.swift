//
//  LikemeeAPIRequest.swift
//  radio.likemee
//
//  Created by <PERSON> on 3/6/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

import UIKit
import DeviceKit

class SessionstoreResponse: APIResponse {
    var sessionid: String?
    
    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.result:
            if let result = value as? [String: Any] {
                if let entryTest = result[API.Parameters.entry] {
                    if let entry = entryTest as? [Any] {
                        if let firstEntry = entry.first as? [String: Any] {
                            if let sessionid = firstEntry["sessionid"] as? String {
                                self.sessionid = sessionid
                            }
                        }
                    }
                }
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}

class LikemeeAPIRequest: APIRequest {
    var originalParams: ParametersType?
    var originalService: ServiceType?
    
    struct Constants {
        struct Headers {
            static var standard: HeadersType {
                return [
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                ]
            }
            
            static var xml: HeadersType {
                return [
                    "Accept": "application/xhtml+xml,application/xml"
                ]
            }
            
            static var startPage: HeadersType {
                var dict = standard
                
                if let eTag = DataManager.shared.startPageMore.eTag {
                    dict["If-None-Match"] = eTag
                }
                dict["cache-control"] = "no-cache"
                
                return dict
            }
            
            static var multipart: HeadersType {
                return [
                    "Accept": "application/json",
                    "Content-Type": "multipart/form-data"
                ]
            }
        }
    }
    
    class Service {
        private var dict: ServiceType = [:]
        
        func get() -> ServiceType {
            return dict
        }
        
        init(
            method: String,
            host: String? = nil,
            service: String? = nil,
            serviceURL: String? = nil,
            headers: HeadersType? = nil
        ) {
            dict["method"] = method
            dict["host"] = host ?? RLMSettings.hostURL
            dict["service"] = service ?? ""
            dict["serviceURL"] = serviceURL ?? ""
            dict["headers"] = headers ?? LikemeeAPIRequest.Constants.Headers.standard
        }
    }
    
    class Parameters {
        private var dict: ParametersType = [:]
        
        func get() -> ParametersType {
            return dict
        }
        
        init() {}
        
        init(
            includePostalCode: Bool = false,
            includeIap: Bool = false,
            includeLocation: Bool = false
        ) {
            if includePostalCode {
                dict[API.Parameters.postalCode] = (LocationService.sharedInstance.postalCode ?? "")
            }
            
            if includeIap {
                dict[API.Parameters.showIAP] = String(includeIap)
            }
            
            if includeLocation {
                if let lat = LocationService.sharedInstance.lat,
                   let lon = LocationService.sharedInstance.lon {
                    if let latNumber = Double(lat), let lonNumber = Double(lon) {
                        let lat2Dec = String(format: "%.2f", latNumber)
                        let lon2Dec = String(format: "%.2f", lonNumber)
                        let locationParamString = lat2Dec + "," + lon2Dec
                        
                        let allowedCharactersForUrlEncoding = CharacterSet.decimalDigits.union(CharacterSet(charactersIn: "."))
                        
                        dict[API.Parameters.l] = locationParamString.addingPercentEncoding(
                            withAllowedCharacters: allowedCharactersForUrlEncoding
                        )
                    }
                }
            }
        }
        
        init(
            epgId: String,
            stationId: String,
            music: Bool,
            sortOrder: String,
            startDatetime: String,
            endDatetime: String
        ) {
            dict[API.Parameters.epgId] = epgId
            dict[API.Parameters.stationId] = stationId
            dict[API.Parameters.music] = String(music)
            dict[API.Parameters.sortOrder] = sortOrder
            
            dict[API.Parameters.startDatetime] = startDatetime
            dict[API.Parameters.endDatetime] = endDatetime
            dict[API.Parameters.postalCode] = (LocationService.sharedInstance.postalCode ?? "")
        }
    }
    
    func getConfig(
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        name = .config
        cachePolicy = .reloadIgnoringLocalCacheData
        
        let serviceURL = ""
        var serviceString = ""
        
        if serviceURL == "" {
            serviceString = RLMSettings.mainConfig
        }
        
        executeGETRequest(
            forService: Service(
                method: "GET",
                service: serviceString,
                serviceURL: serviceURL
            ).get(),
            andParams: Parameters().get(),
            response: nil,
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func getStartpage(
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        name = .startpage
        
        guard let url = DataManager.shared.config.result?.endpoints?.startpage else {
            return
        }
        
        cachePolicy = .reloadIgnoringLocalCacheData
        
        let service = Service(
            method: "GET",
            serviceURL: url,
            headers: LikemeeAPIRequest.Constants.Headers.startPage
        ).get()
        
        executeGETRequest(
            forService: service,
            andParams: Parameters(
                includePostalCode: true,
                includeLocation: true
            ).get(),
            response: nil,
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func getEPG(
        forEpgID epgId: String,
        andStationId stationId: String,
        andMusic music: Bool = true,
        andSortOrder sortOrder: String = "desc",
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        let df = DateFormatter()
        df.dateFormat = Config.dateFormat
        
        if let zone = TimeZone(identifier: "Europe/Berlin") {
            df.timeZone = zone
        }
        
        let startDatetime = df.string(
            from: Date().addingTimeInterval(RLMSettings.epgInterval)
        )
        let endDatetime = df.string(from: Date())
        
        var service = Service(method: "GET").get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.epg ?? ""
        
        let params = Parameters(
            epgId: epgId,
            stationId: stationId,
            music: music,
            sortOrder: sortOrder,
            startDatetime: startDatetime,
            endDatetime: endDatetime
        )
        
        executeGETRequest(
            forService: service,
            andParams: params.get(),
            response: EPGResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func getAlarmSounds(
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        var service = Service(method: "GET").get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.alarmSounds ?? ""
        
        executeGETRequest(
            forService: service,
            andParams: Parameters(includePostalCode: true).get(),
            response: AlarmSoundsResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func getNarrators(
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        var service = Service(method: "GET").get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.narrators ?? ""
        
        executeGETRequest(
            forService: service,
            andParams: Parameters(includeIap: true).get(),
            response: NarratorResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func getAlarmContent(
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        var service = Service(method: "GET").get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.alarmContentTypes ?? ""
        
        executeGETRequest(
            forService: service,
            andParams: Parameters(includePostalCode: true).get(),
            response: AlarmContentResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func getSleepAid(
        withShowIAP _: Bool,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        var service = Service(method: "GET").get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.sleepAids ?? ""
        
        executeGETRequest(
            forService: service,
            andParams: Parameters(includePostalCode: true, includeIap: true).get(),
            response: SleepAidResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func createAlarmSequence(
        withAlarmTime alarmTime: String,
        withPersonalizedName personalizedName: String,
        withRadioStreamId radioStreamId: String?,
        withNarratorId narratorId: String?,
        withIntroJingle introJingle: Bool,
        withSequenceId sequenceId: String,
        withAlarmContentTypes alarmContentTypes: [String],
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        let params: ParametersType = [
            API.Parameters.alarmTime: alarmTime,
            API.Parameters.personalizedName: personalizedName,
            API.Parameters.radioStreamId: radioStreamId ?? "",
            API.Parameters.narratorId: narratorId ?? "",
            API.Parameters.introJingle: String(introJingle),
            API.Parameters.sequenceId: sequenceId,
            API.Parameters.alarmContentTypes: alarmContentTypes,
            API.Parameters.postalCode: LocationService.sharedInstance.postalCode ?? ""
        ]
        
        var service = Service(method: "POST").get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.alarm ?? ""
        
        executeParamsAnyPOSTRequest(
            forService: service,
            andParams: params,
            response: AlarmSequenceResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func deleteAlarmSequence(
        withSequenceId sequenceId: String,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        let params: ParametersType = [
            API.Parameters.sequenceId: sequenceId,
            API.Parameters.postalCode: LocationService.sharedInstance.postalCode ?? ""
        ]
        
        var service = Service(method: "GET").get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.deleteSequence ?? ""
        
        executeGETRequest(
            forService: service,
            andParams: params,
            response: DeleteAlarmSequenceResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func fetchTexts(
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        var service = Service(method: "GET").get()
        
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.texts ?? ""
        
        executeGETRequest(
            forService: service,
            andParams: Parameters().get(),
            response: TextsResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    private func sendUserData(
        token: String,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        let userDataHeader = [
            "X-Api-Key": "SBsTcHy2GpaxYfLQTqgmh6Jg",
            "Accept-Encoding": "application/json",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Authorization": "Bearer " + token
        ]
        
        var params: ParametersType = [
            "token": token,
            "userid": DataManager.shared.store.profile.id ?? "",
            "gender": DataManager.shared.store.profile.gender ?? "",
            "fname": DataManager.shared.store.profile.firstName ?? "",
            "lname": DataManager.shared.store.profile.lastName ?? "",
            "email": DataManager.shared.store.profile.email ?? "",
            "bday": DataManager.shared.store.profile.birthday ?? "",
            "street": DataManager.shared.store.profile.address ?? "",
            "zip": DataManager.shared.store.profile.zip ?? "",
            "city": DataManager.shared.store.profile.city ?? "",
            "channelid": ""
        ]
        
#if USE_AIRSHIP
        let channelID: String = AirshipHelper.shared.airshipChannelID() ?? ""
        params["channelid"] = TrackAirship.shared.trackingEnabled ? channelID : ""
#endif
        
        var service = Service(
            method: "POST",
            host: "https://sessionstore.loverad.io",
            service: "v1",
            headers: userDataHeader
        ).get()
        service["serviceURL"] = "https://sessionstore.loverad.io/v1"
        
        self.executePOSTRequest(
            forService: service,
            andParams: params,
            response: SessionstoreResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func sendUserDataIfUserIsLoggedIn(
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        if let token = RlmLoginManager.sharedInstance.tokenAccess {
            self.sendUserData(
                token: token,
                onSuccess: onSuccess,
                onError: onError
            )
        }
    }
    
    func sendMitmachenMessage(
        messageText: String,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        let emailSubject = DataManager.shared.mitmachenPresets[MitmachenPresets.emailSubject] ?? "messenger_email_subject".localized()
        
        var params: ParametersType = [
            API.Parameters.request: emailSubject,
            API.Parameters.message: messageText,
            API.Parameters.version: "iOS \(UIDevice.current.systemVersion)",
            API.Parameters.device: Device.current.safeDescription
        ]
        
        var fn = ""
        if let name = DataManager.shared.store.profile.firstName {
            fn = name
        }
        
        var ln = ""
        if let lastName = DataManager.shared.store.profile.lastName {
            ln = lastName
        }
        
        var ttl = "title_male".localized()
        if let title = DataManager.shared.store.profile.gender, title != "" {
            ttl = title
        }
        params[API.Parameters.name] = "\(ttl) \(fn) \(ln)"
        
        if let email = DataManager.shared.store.profile.email {
            params[API.Parameters.mail] = email
        } else {
            params[API.Parameters.mail] = ""
        }
        
        if let phone = DataManager.shared.store.profile.phone {
            params[API.Parameters.phone] = phone
        } else {
            params[API.Parameters.phone] = ""
        }
        
        var service = Service(
            method: "POST",
            headers: LikemeeAPIRequest.Constants.Headers.multipart
        ).get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.contactForm ?? ""
        
        executeMultipartPOSTRequest(
            forService: service,
            andParams: params,
            mediaItemParamsKey: "media",
            response: MitmachenMessageResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func sendMitmachenRecording(
        recordingData: Data,
        recordingName: String,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        let emailSubject = DataManager.shared.mitmachenPresets[MitmachenPresets.emailSubject] ?? "messenger_email_subject".localized()
        
        var params: ParametersType = [
            API.Parameters.request: emailSubject,
            API.Parameters.media: recordingData,
            API.Parameters.version: "iOS \(UIDevice.current.systemVersion)",
            API.Parameters.device: Device.current.safeDescription,
            "mediaName": recordingName
        ]
        
        var fn = ""
        if let name = DataManager.shared.store.profile.firstName {
            fn = name
        }
        
        var ln = ""
        if let lastName = DataManager.shared.store.profile.lastName {
            ln = lastName
        }
        
        var ttl = "title_male".localized()
        if let title = DataManager.shared.store.profile.gender, title != "" {
            ttl = title
        }
        params[API.Parameters.name] = "\(ttl) \(fn) \(ln)"
        
        if let email = DataManager.shared.store.profile.email {
            params[API.Parameters.mail] = email
        } else {
            params[API.Parameters.mail] = ""
        }
        
        if let phone = DataManager.shared.store.profile.phone {
            params[API.Parameters.phone] = phone
        } else {
            params[API.Parameters.phone] = ""
        }
        
        var service = Service(
            method: "POST",
            headers: LikemeeAPIRequest.Constants.Headers.multipart
        ).get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.contactForm ?? ""
        
        executeMultipartPOSTRequest(
            forService: service,
            andParams: params,
            mediaItemParamsKey: API.Parameters.media,
            response: MitmachenMessageResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func sendMitmachenImage(
        _ image: UIImage,
        imageName: String,
        WithText imageText: String,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        let emailSubject = DataManager.shared.mitmachenPresets[MitmachenPresets.emailSubject] ?? "messenger_email_subject".localized()
        
        var params: ParametersType = [
            API.Parameters.request: emailSubject,
            API.Parameters.media: image,
            API.Parameters.message: imageText,
            API.Parameters.version: "iOS \(UIDevice.current.systemVersion)",
            API.Parameters.device: Device.current.safeDescription,
            "mediaName": imageName
        ]
        
        var fn = ""
        if let name = DataManager.shared.store.profile.firstName {
            fn = name
        }
        
        var ln = ""
        if let lastName = DataManager.shared.store.profile.lastName {
            ln = lastName
        }
        
        var ttl = "title_mail".localized()
        if let title = DataManager.shared.store.profile.gender, title != "" {
            ttl = title
        }
        params[API.Parameters.name] = "\(ttl) \(fn) \(ln)"
        
        if let email = DataManager.shared.store.profile.email {
            params[API.Parameters.mail] = email
        } else {
            params[API.Parameters.mail] = ""
        }
        
        if let phone = DataManager.shared.store.profile.phone {
            params[API.Parameters.phone] = phone
        } else {
            params[API.Parameters.phone] = ""
        }
        
        var service = Service(
            method: "POST",
            headers: LikemeeAPIRequest.Constants.Headers.multipart
        ).get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.contactForm ?? ""
        
        executeMultipartPOSTRequest(
            forService: service,
            andParams: params,
            mediaItemParamsKey: API.Parameters.media,
            response: MitmachenMessageResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func sendContactMessage(
        formData: ContactFormFields,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        let emailSubject = "feedback_subject".localized(["issue": formData.type])
        var params: ParametersType = [
            API.Parameters.request: emailSubject,
            API.Parameters.message: formData.message,
            API.Parameters.version: "iOS \(UIDevice.current.systemVersion)",
            API.Parameters.device: Device.current.safeDescription
        ]
        
        params[API.Parameters.name] = formData.name
        params[API.Parameters.mail] = formData.email
        params[API.Parameters.phone] = formData.phone
        
        var service = Service(
            method: "POST",
            headers: LikemeeAPIRequest.Constants.Headers.multipart
        ).get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.contactForm ?? ""
        
        executeMultipartPOSTRequest(
            forService: service,
            andParams: params,
            mediaItemParamsKey: "media",
            response: MitmachenMessageResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func requestIRISBanner(
        ForPostalCode postalCode: String,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        var service = Service(method: "GET").get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.displayAds ?? ""
        
        executeGETRequest(
            forService: service,
            andParams: Parameters(includePostalCode: true).get(),
            response: AdBannerResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    private func executeGETRequest(
        forService service: ServiceType,
        andParams params: ParametersType? = [:],
        response: APIResponse?, // FIXME: remove once complete json deserialization is done
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        originalService = service
        originalParams = params
        originalHandlerSuccess = onSuccess
        originalHandlerError = onError
        
        if let params = originalParams, let service = originalService {
            executeGETRequestWithParameters(
                params: params,
                service: service,
                responseObject: response,
                onSuccess: onSuccess,
                onError: onError
            )
        }
    }
    
    private func executePOSTRequest(
        forService service: ServiceType,
        andParams params: ParametersType? = [:],
        response: APIResponse,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        originalService = service
        originalParams = params
        originalHandlerSuccess = onSuccess
        originalHandlerError = onError
        
        if let params = originalParams, let service = originalService {
            executeJSONPOSTRequestWithParameters(
                params: params,
                service: service,
                responseObject: response,
                onSuccess: onSuccess,
                onError: onError
            )
        }
    }
    
    private func executeMultipartPOSTRequest(
        forService service: ServiceType,
        andParams params: ParametersType? = [:],
        mediaItemParamsKey: String,
        response: APIResponse,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        originalService = service
        originalParams = params
        originalHandlerSuccess = onSuccess
        originalHandlerError = onError
        
        if let params = originalParams, let service = originalService {
            executeMultipartPOSTRequestWithParameters(
                params: params,
                mediaItemParamsKey: mediaItemParamsKey,
                service: service,
                responseObject: response,
                onSuccess: onSuccess,
                onError: onError
            )
        }
    }
    
    private func executeParamsAnyPOSTRequest(
        forService service: ServiceType,
        andParams params: ParametersType? = [:],
        response: APIResponse,
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        originalService = service
        originalParams = params
        originalHandlerSuccess = onSuccess
        originalHandlerError = onError
        
        if let params = originalParams, let service = originalService {
            executeJSONPOSTRequestWithParameters(
                params: params,
                service: service,
                responseObject: response,
                onSuccess: onSuccess,
                onError: onError
            )
        }
    }
    
    func getPreroll(
        forService service: ServiceType,
        andParams params: ParametersType? = [:],
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        executeGETRequest(
            forService: service,
            andParams: params,
            response: VastResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    func trackPreroll(
        withURL url: String?,
        onSuccess: HandlerSuccessType = nil,
        onError: HandlerFailureType = nil
    ) {
        var s = Service(method: "GET").get()
        
        if let url = url {
            s["serviceURL"] = url
        }
        
        executeGETRequest(
            forService: s,
            andParams: [:],
            response: nil,
            onSuccess: onSuccess,
            onError: onError
        )
    }
    
    override func showSpinnerInView(view: UIView) {
        DispatchQueue.main.async {
            if self.waitingView != nil {
                return
            }
            
            var frame = view.frame
            frame.origin = .zero
            
            self.waitingView = UIView(frame: frame)
            if let waitingView = self.waitingView {
                waitingView.backgroundColor = UIColor(white: 0.0, alpha: 0.3)
                waitingView.startActivityIndicator(39.0)
                
                view.addSubview(waitingView)
            }
        }
    }
    
    func postParticipation(
        data: [String: FormElement],
        onSuccess: HandlerSuccessType,
        onError: HandlerFailureType
    ) {
        var service = Service(method: "POST").get()
        service["serviceURL"] = DataManager.shared.config.result?.endpoints?.participatePost ?? ""
        
        var params = [String: Any]()
        var mediaKeys = [String]()
        
        for (key, element) in data {
            switch element.type {
            case .uploadImage:
                if let data = element.data as? UIImage {
                    params[key] = data
                    mediaKeys.append(key)
                }
            case .recordAudio:
                if let urlString = (element.data as? String),
                   let url = URL(string: urlString) {
                    do {
                        let data = try Data(contentsOf: url)
                        params[key] = data
                        mediaKeys.append(key)
                    } catch {
                        
                    }
                }
            case .unknown:
                break
            default:
                params[key] = "\(element.data)"
            }
        }
        
        executeMultipartPOSTRequestWithParameters(
            params: params,
            mediaItemParamsKeys: mediaKeys,
            service: service,
            responseObject: APIResponse(),
            onSuccess: onSuccess,
            onError: onError
        )
    }
}
