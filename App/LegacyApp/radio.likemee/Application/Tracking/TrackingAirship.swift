//
//  TrackingAirship.swift
//  radio.likemee
//
//  Created by <PERSON> on 04/03/2021.
//  Copyright © 2021 Computer Rock. All rights reserved.
//

import Foundation

#if USE_AIRSHIP
final class TrackAirship {
    static let shared: TrackAirship = TrackAirship()
    private var setupDone = false
    var takeOffAlreadyCalled = false
    
    internal var _trackingEnabled: Bool = UserDefaults.standard.object(
        forKey: TrackingKeys.isAirshipTrackingEnabledKey
    ) as? Bool ?? false
    
    // Should always be called after setting the TrackingSqs.isTrackingEnabled.
    var trackingEnabled: Bool {
        get {
            return _trackingEnabled
        }
        set {
            setConsent(given: newValue)
        }
    }
    
    private init() {}
    
    private func changeConsentState(given: Bool) {
        Util.debugLog("🛤 CONSENT \(given)")
        
        DispatchQueue.main.async {
            let logState = {
                Util.debugLog("🛤 Airship tracking is \(given ? "enabled" : "disabled")")
                if TrackAirship.shared.trackingEnabled == true {
                    Util.debugLog("🛤 Airship features are \(AirshipHelper.shared.isAnyFeatureEnabled() ? "ENABLED" : "DISABLED")")
                }
            }
            
            if given {
                self._trackingEnabled = given
                
                UserDefaults.standard.set(given, forKey: TrackingKeys.isAirshipTrackingEnabledKey)
                UserDefaults.standard.synchronize()
                
                AirshipHelper.shared.setNotifications()
                AirshipHelper.shared.enableAllFeatures()
                
                AirshipHelper.shared.updateOptInTag(
                    forTracking: "personalisierte_inhalte",
                    given
                )
                AirshipHelper.shared.add(
                    tags: ["allow_push_notifications"],
                    group: RLMSettings.airshipPrivacyGroup
                )
                DataManager.shared.store.pushTopics.register()
                
                self.setupDone = true
                logState()
            } else {
                AirshipHelper.shared.land()
                
                AirshipHelper.shared.remove(
                    tags: [
                        "opt_in_personalisierte_inhalte",
                        "allow_push_notifications"
                    ],
                    group: RLMSettings.airshipPrivacyGroup
                )
                AirshipHelper.shared.remove(
                    tags: DataManager.shared.store.pushTopics.acceptedTags,
                    group: RLMSettings.airshipPushNotificationsGroup
                )
                AirshipHelper.shared.disableAllFeatures()
                
                self._trackingEnabled = given
                
                UserDefaults.standard.set(given, forKey: TrackingKeys.isAirshipTrackingEnabledKey)
                UserDefaults.standard.synchronize()
                
                logState()
            }
        }
    }
    
    func setConsent(given: Bool) {
        if given {
            changeConsentState(given: given)
            DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(1)) {
                TrackerUtil.shared.updatePrivacyTags()
            }
        } else {
            // NOTE: At this point the SQS tracking should be disabled already
            // if not the personalized tracking tag will remain set
            changeConsentState(given: given)
        }
    }
    
    func onDidFinishLaunching() {}
    
    func onAppOpen() {}
    
    func send(event: String, value: NSNumber, properties: [String: Any]? = nil) {
        guard setupDone else {
            return
        }
        
        AirshipHelper.shared.send(
            eventName: event,
            value: value,
            properties: properties
        )
    }
    
    func send(event: String, properties: [String: Any]? = nil) {
        guard setupDone, trackingEnabled else {
            return
        }
        
        AirshipHelper.shared.send(eventName: event, properties: properties)
    }
    
    @MainActor func trackScreen(_ screenName: String) {
        guard setupDone else {
            return
        }
        Util.debugLog("🛤 track '\(screenName)' screen view")
        AirshipHelper.shared.trackScreen(screenName)
    }
    
    func setLoggedInTags() {
        AirshipHelper.shared.add(
            tags: ["logged_in"],
            group: RLMSettings.airshipAccountGroup
        )
        AirshipHelper.shared.applyMutations(profile: DataManager.shared.store.profile)
        AirshipHelper.shared.mergeFavorites(FavoritesManager.favoritedElements())
    }
    
    func favorite(id: String?, title: String?, followed: Bool, isPodcast: Bool) {
        if followed {
            AirshipHelper.shared.add(
                userTags: [
                    "\(isPodcast ? "podcast" : "stream")_\(id ?? "")",
                    "\(isPodcast ? "podcast" : "stream")_\(title ?? "")"
                ],
                group: RLMSettings.airshipFavouritesGroup
            )
        } else {
            AirshipHelper.shared.remove(
                userTags: [
                    "\(isPodcast ? "podcast" : "stream")_\(id ?? "")",
                    "\(isPodcast ? "podcast" : "stream")_\(title ?? "")"
                ],
                group: RLMSettings.airshipFavouritesGroup
            )
        }
    }
}
#else
final class TrackAirship {
    static let shared: TrackAirship = TrackAirship()
    private var setupDone = false
    
    internal var _trackingEnabled: Bool = false
    
    var trackingEnabled: Bool {
        get {
            return false
        }
        set {}
    }
    
    private init() {}
    
    private func setup(force: Bool = false) {}
    
    func setConsent(given: Bool) {}
    
    func onDidFinishLaunching() {}
    
    func onAppOpen() {}
    
    func send(event: String, value: NSNumber, properties: [String: Any]? = nil) {}
    
    func send(event: String, value: String, properties: [String: Any]? = nil) {}
    
    func send(event: String, properties: [String: Any]? = nil) {}
    
    func trackScreen(_ screenName: String) {}
    
    func setLoggedInTags() {}
    
    func favorite(id: String?, title: String?, followed: Bool, isPodcast: Bool) {}
}
#endif
