//
//  TrackOnePlusX.swift
//  radio.likemee
//
//  Created by <PERSON><PERSON><PERSON> on 5/23/19.
//  Copyright © 2019 Computer Rock. All rights reserved.
//

import Foundation

#if USE_ONEPLUSX
import AppTrackingTransparency
import OnePlusX
import DeviceKit

// FIXME: remove NSObject once reachability is removed from extension
class TrackOnePlusX: NSObject, Loggable {
#if USE_USERCENTRICS
    internal var _isTrackingEnabled: Bool = false
    internal var _isEmetriqTrackingEnabled: Bool = false
#else
    internal var _isTrackingEnabled: Bool = UserDefaults.standard.object(
        forKey: TrackingKeys.isOnePlusXTrackingEnabledKey
    ) as? Bool ?? false
    internal var _isEmetriqTrackingEnabled: Bool = UserDefaults.standard.object(
        forKey: TrackingKeys.isOnePlusXEmetriqTrackingEnabledKey
    ) as? Bool ?? false
#endif
    
    private var segments: [String] = []
    private var emetriqSegments: [String] = []
    private var emetriqSegmentsInProgress: Bool = false
    private lazy var screenResolution: String = {
        let screen = UIScreen.main
        let screenResulutionHeight = screen.bounds.height * screen.scale
        let screenResulutionWidth = screen.bounds.width * screen.scale
        return "\(Int(screenResulutionHeight))x\(screenResulutionWidth)"
    }()
    
    var emetriqTCString: String = ""
    
    var isTrackingEnabled: Bool {
        get {
            return self._isTrackingEnabled
        }
        set {
            setConsent(given: newValue)
            self._isTrackingEnabled = newValue
            
#if !USE_USERCENTRICS
            UserDefaults.standard.set(
                self._isTrackingEnabled,
                forKey: TrackingKeys.isOnePlusXTrackingEnabledKey
            )
            UserDefaults.standard.synchronize()
#endif
        }
    }
    
    var isEmetriqTrackingEnabled: Bool {
        get {
            return self._isEmetriqTrackingEnabled
        }
        
        set {
            self._isEmetriqTrackingEnabled = newValue
            if newValue && self._isEmetriqTrackingEnabled {
                fetchEmetriqSegments()
            }
            
#if !USE_USERCENTRICS
            UserDefaults.standard.set(
                self._isEmetriqTrackingEnabled,
                forKey: TrackingKeys.isOnePlusXEmetriqTrackingEnabledKey
            )
            UserDefaults.standard.synchronize()
#endif
        }
    }
    
    var isSetupDone = false
    private var isInitEventSent = false
    
    private override init() {
        super.init()
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(reachabilityChanged(_:)),
            name: .reachabilityChanged,
            object: nil
        )
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc func reachabilityChanged(_ notification: Notification) {
        if ReachabilityUtils.shared.status?.onlineStatus() == .online &&
            ATTrackingManager.trackingAuthorizationStatus != .notDetermined {
            setConsent(given: isTrackingEnabled, isGoingOnline: true)
        }
    }
    
    private func setup(force: Bool = false) {
        askForTrackingPermission { [weak self] _ in
            if self?.isSetupDone == true || (!force && self?.isTrackingEnabled == false) {
                if self?.isInitEventSent == false {
                    self?.sendInitEvent()
                }
                return
            }
            
#if DEBUG
            Ope.start(
                dataCollectionApiIdentifier: Tracking.OnePlusX.clientID,
                enablePublicKeyPinning: false,
                log: self
            )
#else
            Ope.start(
                dataCollectionApiIdentifier: Tracking.OnePlusX.clientID
            )
#endif
            
            self?.isSetupDone = true
            self?.sendInitEvent()
            
            if ReachabilityUtils.shared.status?.onlineStatus() == .online {
                self?.setConsent(given: self?.isTrackingEnabled ?? false)
            }
            
            Util.debugLog("🛤 Setup done!")
        }
    }
    
    static var shared: TrackOnePlusX = {
        let instance = TrackOnePlusX()
        return instance
    }()
    
    func onDidFinishLaunching() {}
    
    func onAppOpen() {
        sendInitEvent()
    }
    
    private func sendInitEvent() {
        guard self.isTrackingEnabled == true,
              let installationID = DataManager.shared.startPage.config?[0].adswizz_installation_id,
              self.isInitEventSent == false else {
            return
        }
        
        let event = createInitEvent(installationID: installationID)
        try? Ope.trackEvent(event)
        self.isInitEventSent = true
    }
    
    func createInitEvent(installationID: String) -> Event {
        let event = OnePlusXInitEvent(
            cpdPubId: RLMSettings.onePlusXPublisherID,
            siteContentDomain: DataManager.shared.startPage.config?[0].adswizz_player_id_ios ?? "",
            techDeviceName: Device.current.safeDescription,
            techDeviceOs: "iOS",
            techDeviceScreenResolution: screenResolution,
            geoGpsLat: LocationService.sharedInstance.lat,
            geoGpsLong: LocationService.sharedInstance.lon,
            partnerEmetriqSegmentsApp: getEmetriqSegments(),
            idAdswizz: IDFAHelper.shared.identifier ?? IDFAHelper.randomId ?? "",
            opeAltUserIDs: getOpeAltUserIDs(),
            opeItemURI: installationID
        )
        
        guard ATTrackingManager.trackingAuthorizationStatus == .authorized,
              let email = DataManager.shared.store.profile.email else {
            return event
        }
        
        return OnePlusXInitEmailEvent(
            idHashedEmail: email.sha256(),
            initEvent: event
        )
    }
    
    func sendLogin() {
        guard self.isTrackingEnabled == true,
              let installationID = DataManager.shared.startPage.config?[0].adswizz_installation_id else {
            return
        }
        
        let gender = Gender.from(text: DataManager.shared.store.profile.gender)?.symbol ?? ""
        
        let event = OnePlusXInitLoginEvent(
            cpdPubId: RLMSettings.onePlusXPublisherID,
            siteContentDomain: DataManager.shared.startPage.config?[0].adswizz_player_id_ios ?? "",
            techDeviceName: Device.current.safeDescription,
            techDeviceOs: "iOS",
            techDeviceScreenResolution: screenResolution,
            geoGpsLat: LocationService.sharedInstance.lat,
            geoGpsLong: LocationService.sharedInstance.lon,
            partnerEmetriqSegmentsApp: getEmetriqSegments(),
            idHashedEmail: DataManager.shared.store.profile.email?.sha256() ?? "",
            idAdswizz: IDFAHelper.shared.identifier ?? IDFAHelper.randomId ?? "",
            loginDeclaredGender: gender,
            loginDeclaredDateOfBirth: DataManager.shared.store.profile.birthday,
            loginDeclaredCity: DataManager.shared.store.profile.city,
            loginDeclaredZip: DataManager.shared.store.profile.zip,
            opeAltUserIDs: getOpeAltUserIDs(),
            opeItemURI: installationID
        )
        try? Ope.trackEvent(event)
    }
    
    func sendStreamPlay(
        forElement element: ResponseObjectMainConfigStartPageElement?,
        withMinutes minutes: Int
    ) {
        guard self.isTrackingEnabled == true,
              let installationID = DataManager.shared.startPage.config?[0].adswizz_installation_id else {
            return
        }
        
        let streamContentId = "\(installationID)_\(element?.mc_flow_id ?? "")"
        let hour = Calendar.current.component(.hour, from: Date())
        let listeningHour = "\(hour)-\(hour + 1)"
        var taxonomyArray = element?.taxonomy?.tone
        taxonomyArray?.append(contentsOf: element?.taxonomy?.ttwo ?? [])
        taxonomyArray?.append(contentsOf: element?.taxonomy?.tthree ?? [])
        taxonomyArray?.append(contentsOf: element?.taxonomy?.tfour ?? [])
        let taxonomyString = taxonomyArray?.joined(separator: ",")
        let taxonomyIdsString = element?.taxonomy?.tid?.joined(separator: ",")
        
        var opeItemURI: String = installationID
        if let streamID = element?.mc_flow_id {
            opeItemURI += "_ios:streamplay-\(streamID)"
        } else {
            opeItemURI += "_ios:streamplay"
        }
        
        let event = OnePlusXStreamPlayEvent(
            streamContentName: element?.element_name,
            streamContentId: streamContentId,
            streamContentListeningHour: listeningHour,
            streamContentPlayed: minutes,
            streamTaxonomyIABName: taxonomyString,
            streamTaxonomyIABId: taxonomyIdsString,
            opeAltUserIDs: getOpeAltUserIDs(),
            opeItemURI: opeItemURI
        )
        try? Ope.trackEvent(event)
    }
    
    func sendPodcastPlay(
        forSerie serie: RlmDataPodcast.Serie?,
        episode: RlmDataPodcast.Episode?
    ) {
        guard self.isTrackingEnabled == true,
              let installationID = DataManager.shared.startPage.config?[0].adswizz_installation_id else {
            return
        }
        
        let event = OnePlusXAodPodcastPlayEvent(
            podcastContentSeriesName: serie?.title,
            podcastContentSeriesId: serie?.uuid,
            podcastContentEpisodeId: episode?.uuid,
            podcastContentEpisodeName: episode?.title,
            opeAltUserIDs: getOpeAltUserIDs(),
            opeItemURI: installationID
        )
        try? Ope.trackEvent(event)
    }
    
    func sendAodPlay(
        forElement element: ResponseObjectMainConfigStartPageElement?
    ) {
        guard self.isTrackingEnabled == true,
              let installationID = DataManager.shared.startPage.config?[0].adswizz_installation_id else {
            return
        }
        
        let event = OnePlusXAodPlayEvent(
            aodContentName: element?.element_name,
            aodContentId: element?.element_iris_series_id,
            opeAltUserIDs: getOpeAltUserIDs(),
            opeItemURI: installationID
        )
        try? Ope.trackEvent(event)
    }
    
    func setConsent(
        given: Bool,
        isAppStart: Bool = false,
        isGoingOnline: Bool = false
    ) {
        let noChange = (given == isTrackingEnabled)
        let force = (isAppStart && given == true) || isGoingOnline
        let status = ReachabilityUtils.shared.status?.onlineStatus()
        
        if (status != .online) || (noChange && !force) {
            return
        }
        
        setup(force: given == true)
        
        if given {
            LikemeeAPIRequest.getOnePlusXSegments().then { [weak self] segments in
                UIWindow.activityHide()
                self?.segments = segments
            }.onError { _ in
                UIWindow.activityHide()
            }
        }
        
        AirshipHelper.shared.updateOptInTag(forTracking: "1plusx", given)
        Util.debugLog("🛤 CONSENT \(given)")
    }
    
    func getOnePlusXPercentEncodedSegments() -> String? {
        return getOnePlusXSegments()?.addingPercentEncoding(withAllowedCharacters: .alphanumerics)
    }
    
    func getOnePlusXSegments() -> String? {
        var segments_text: String?
        
        if isTrackingEnabled, segments.count > 0 {
            segments_text = (segments as NSArray).componentsJoined(by: ",")
        }
        
        return segments_text
    }
    
    func fetchEmetriqSegments() {
        guard ATTrackingManager.trackingAuthorizationStatus == .authorized,
              !emetriqSegmentsInProgress,
              emetriqSegments.isEmpty else {
            return
        }
        
        emetriqSegmentsInProgress = true
        
        LikemeeAPIRequest
            .getEmetriqSegments(forTCString: emetriqTCString)
            .then { [weak self] segments in
                self?.emetriqSegments = segments
            }.finally { [weak self] in
                self?.emetriqSegmentsInProgress = false
            }
    }
    
    func getEmetriqSegments() -> String? {
        var segments_text: String?
        
        if isEmetriqTrackingEnabled, emetriqSegments.count > 0 {
            segments_text = (emetriqSegments as NSArray).componentsJoined(by: ",")
        }
        
        return segments_text
    }
    
    func getOpeAltUserIDs() -> String {
        let hashedEmail = DataManager.shared.store.profile.email?.sha256()
        let idAdswizz = IDFAHelper.shared.identifier ?? IDFAHelper.randomId
        
        var opeAltUserIDs: [String] = []
        
        if let idAdswizz {
            opeAltUserIDs.append("id_adswizz:\(idAdswizz)")
        }
        if ATTrackingManager.trackingAuthorizationStatus == .authorized, let hashedEmail {
            opeAltUserIDs.append("id_hashed_email:\(hashedEmail)")
        }
        
        return opeAltUserIDs.joined(separator: ",")
    }
    
    func log(_ message: String, level: LogLevel, file: StaticString, function: StaticString, line: UInt) {
        if Util.Loggers.onePlusX {
            print("1plusX: " + message)
        }
    }
}
#else
class TrackOnePlusX: NSObject {
    var isTrackingEnabled: Bool = false
    var isSetupDone = false
    var id: String?
    
    private var segments: [String] = []
    private var emetriqSegments: [String] = []
    var emetriqTCString: String = ""
    
    var isEmetriqTrackingEnabled: Bool = false
    
    private func setup(force: Bool = false) {}
    
    static var shared: TrackOnePlusX = {
        let instance = TrackOnePlusX()
        return instance
    }()
    
    var ok: Bool = true
    
    func onDidFinishLaunching() {}
    
    func onAppOpen() {}
    
    func sendLogin() {}
    
    func sendStreamPlay(
        forElement element: ResponseObjectMainConfigStartPageElement?,
        withMinutes: Int
    ) {}
    
    func sendPodcastPlay(
        forSerie serie: RlmDataPodcast.Serie?,
        episode: RlmDataPodcast.Episode?
    ) {}
    
    func sendAodPlay(
        forElement element: ResponseObjectMainConfigStartPageElement?
    ) {}
    
    func setConsent(
        given: Bool,
        isAppStart: Bool = false,
        isGoingOnline: Bool = false
    ) {}
    
    func getOnePlusXPercentEncodedSegments() -> String? {
        return nil
    }
    
    func getOnePlusXSegments() -> String? {
        return nil
    }
}
#endif
